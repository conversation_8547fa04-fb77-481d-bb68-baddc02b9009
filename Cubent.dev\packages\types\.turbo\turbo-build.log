
> @cubent/types@0.0.0 build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\packages\types
> tsup

[34mCLI[39m Building entry: src/index.ts
[34mCLI[39m Using tsconfig: tsconfig.json
[34mCLI[39m tsup v8.5.0
[34mCLI[39m Using tsup config: C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\packages\types\tsup.config.ts
[34mCLI[39m Target: es2022
[34mCJS[39m Build start
[34mESM[39m Build start
[32mCJS[39m [1mdist\index.cjs     [22m[32m113.33 KB[39m
[32mCJS[39m [1mdist\index.cjs.map [22m[32m184.50 KB[39m
[32mCJS[39m ⚡️ Build success in 151ms
[32mESM[39m [1mdist\index.js     [22m[32m99.92 KB[39m
[32mESM[39m [1mdist\index.js.map [22m[32m183.62 KB[39m
[32mESM[39m ⚡️ Build success in 150ms
[34mDTS[39m Build start
[32mDTS[39m ⚡️ Build success in 6392ms
[32mDTS[39m [1mdist\index.d.cts [22m[32m585.98 KB[39m
[32mDTS[39m [1mdist\index.d.ts  [22m[32m585.98 KB[39m
