0000000000000000000000000000000000000000 212388691cf32c90cba506df705034b6e6fdff27 QAPT User <<EMAIL>> 1750085144 +0200	commit (initial): ✨ Initial commit
212388691cf32c90cba506df705034b6e6fdff27 e09b43ccf540a28ccf8370dbce13c331015f2a84 QAPT User <<EMAIL>> 1750102199 +0200	commit: Add mockup section with VS Code screenshot to homepage
e09b43ccf540a28ccf8370dbce13c331015f2a84 e09b43ccf540a28ccf8370dbce13c331015f2a84 QAPT User <<EMAIL>> 1750102516 +0200	Branch: renamed refs/heads/master to refs/heads/main
e09b43ccf540a28ccf8370dbce13c331015f2a84 44aebb6c0406ca0d0889564c60313f32d7c261e3 QAPT User <<EMAIL>> 1750105291 +0200	commit: Fix BaseHub import and TypeScript errors for Vercel deployment
44aebb6c0406ca0d0889564c60313f32d7c261e3 d9516e9daddaaf510de6649acada596fa2dd8984 QAPT User <<EMAIL>> 1750105389 +0200	commit: Add sensitive files to gitignore and remove from tracking
d9516e9daddaaf510de6649acada596fa2dd8984 e09b43ccf540a28ccf8370dbce13c331015f2a84 QAPT User <<EMAIL>> 1750105411 +0200	reset: moving to e09b43c
e09b43ccf540a28ccf8370dbce13c331015f2a84 72ff5b5867a40a368bc5463724f2cd8aa86ed2e0 QAPT User <<EMAIL>> 1750105469 +0200	commit: Fix BaseHub import and TypeScript errors for Vercel deployment
72ff5b5867a40a368bc5463724f2cd8aa86ed2e0 bbc0595c4ff3ac84421f6dc1b1dcb395915d480e QAPT User <<EMAIL>> 1750114451 +0200	commit: Fix all BaseHub component imports and remaining TypeScript errors
bbc0595c4ff3ac84421f6dc1b1dcb395915d480e ec51bf1ab5d152af88495871c9d9861b9448ed8d QAPT User <<EMAIL>> 1750116018 +0200	commit: Remove multi-language support, keep only English
ec51bf1ab5d152af88495871c9d9861b9448ed8d a9c3e9eac6d4524100d0df895fd8f9e4ae62c1af QAPT User <<EMAIL>> 1750116153 +0200	commit: Fix TypeScript error in footer component
a9c3e9eac6d4524100d0df895fd8f9e4ae62c1af a8190cc8a2c02897783b77d395d3c6ad368efdaa QAPT User <<EMAIL>> 1750116320 +0200	commit: Fix another TypeScript error in footer component
a8190cc8a2c02897783b77d395d3c6ad368efdaa a8893612ab43e5828ea3f5b9c01889646b0c77dc QAPT User <<EMAIL>> 1750117888 +0200	commit: Fix image display: exclude /images path from middleware and convert mockup to client component
a8893612ab43e5828ea3f5b9c01889646b0c77dc 38f92b927951a887104cc4de15e31b4221f46698 QAPT User <<EMAIL>> 1750122793 +0200	commit: Reduce spacing between hero buttons and mockup image
38f92b927951a887104cc4de15e31b4221f46698 0b36c1a3fa66cdf2f89e3fb7ac22eb30a62e45d1 QAPT User <<EMAIL>> 1750123132 +0200	commit: Reduce top spacing between header and hero content
0b36c1a3fa66cdf2f89e3fb7ac22eb30a62e45d1 c82dde7e9d87c73e819c25817f7e8df60a6d8dd4 QAPT User <<EMAIL>> 1750124131 +0200	commit: Add Vercel config to build only web app and fix deployment
c82dde7e9d87c73e819c25817f7e8df60a6d8dd4 8ee76f8fb6348c49dcd2e04277a0bfb7d5956d70 QAPT User <<EMAIL>> 1750124919 +0200	commit: Add comment to hero component
8ee76f8fb6348c49dcd2e04277a0bfb7d5956d70 56a24933ea5b9c86283ce489350ee348947fa264 QAPT User <<EMAIL>> 1750175944 +0200	commit: Implement complete user profile system with VS Code extension integration
56a24933ea5b9c86283ce489350ee348947fa264 65742637549aa12d67e6a50d2f94ff7ce6368aa9 QAPT User <<EMAIL>> 1750176582 +0200	commit: Fix build error: Add sonner dependency to apps/app
65742637549aa12d67e6a50d2f94ff7ce6368aa9 5d9812c82e4be21a61ae6465d85708987223e675 QAPT User <<EMAIL>> 1750177850 +0200	commit: Update sidebar and redirect URLs for profile-focused navigation
5d9812c82e4be21a61ae6465d85708987223e675 d0ddc9baa1acc86893c3c7b658abbf18a7d4e439 QAPT User <<EMAIL>> 1750178480 +0200	commit: Simplify sidebar navigation and fix redirect URLs
d0ddc9baa1acc86893c3c7b658abbf18a7d4e439 f7b349f54f92afd2120c911ec8c30ee55424acb9 QAPT User <<EMAIL>> 1750179316 +0200	commit: Fix TypeScript error in sidebar component
f7b349f54f92afd2120c911ec8c30ee55424acb9 665bad1cd64957c79683da681b4396e1e2865899 QAPT User <<EMAIL>> 1750179535 +0200	commit: Remove projects section from sidebar to fix TypeScript error
665bad1cd64957c79683da681b4396e1e2865899 23c71dc385493cfa13574401d11f8ec87724febc QAPT User <<EMAIL>> 1750180754 +0200	commit: Add comprehensive VS Code extension API endpoints
23c71dc385493cfa13574401d11f8ec87724febc c8664a93bdce6bce029b98aa2d45da331f1bd839 QAPT User <<EMAIL>> 1750185828 +0200	commit: Add extension analytics and settings sync endpoints
c8664a93bdce6bce029b98aa2d45da331f1bd839 1f730b031470ce85220dfd03486cda9dea521116 QAPT User <<EMAIL>> 1750186109 +0200	commit: Enhance extension connection status and add session management
1f730b031470ce85220dfd03486cda9dea521116 f855b115c4b0212757e643d02139e18efd4c1145 QAPT User <<EMAIL>> 1750186242 +0200	commit: Add comprehensive extension integration documentation and testing
f855b115c4b0212757e643d02139e18efd4c1145 ece1b5ee0abd35d31a0342b77cd9ae36e1b0cc29 QAPT User <<EMAIL>> 1750186767 +0200	commit: Fix database schema for extension integration
ece1b5ee0abd35d31a0342b77cd9ae36e1b0cc29 36a34517392ea1824f638b3c0f7a2f2aae6cd71f QAPT User <<EMAIL>> 1750187474 +0200	commit: Fix build errors and update API endpoint URLs
36a34517392ea1824f638b3c0f7a2f2aae6cd71f f6365fa1f93e976bfc54b1ab9906131896f648ff QAPT User <<EMAIL>> 1750187842 +0200	commit: Fix ExtensionSessionsList component interface
f6365fa1f93e976bfc54b1ab9906131896f648ff 09d3ba4e1b6a4236fc9b36191c5adb2936e1ef8f QAPT User <<EMAIL>> 1750188106 +0200	commit: Fix remaining lastSeen references in profile page
09d3ba4e1b6a4236fc9b36191c5adb2936e1ef8f 6ac8997071eccbdcbd19e28267d62f17fe71979c QAPT User <<EMAIL>> 1750188397 +0200	commit: Fix TypeScript error in API keys route
6ac8997071eccbdcbd19e28267d62f17fe71979c 318ed79896a16598581fa4df97f819bed3ea0093 QAPT User <<EMAIL>> 1750188574 +0200	commit: Fix extension auth route database queries
318ed79896a16598581fa4df97f819bed3ea0093 f92ca666694cc6f8fc5e0768d780d9a55150f0a6 QAPT User <<EMAIL>> 1750190111 +0200	commit: Trigger deployment - extension integration ready
f92ca666694cc6f8fc5e0768d780d9a55150f0a6 f8052678c746167a7bb96e9ef128989cdb30928c QAPT User <<EMAIL>> 1750190743 +0200	commit: Fix extension connect route compound unique key
f8052678c746167a7bb96e9ef128989cdb30928c 96abd10265f8c3507f7da05dc4ec857a0e340ece QAPT User <<EMAIL>> 1750191672 +0200	commit: Add explanatory comment for compound unique key fix
96abd10265f8c3507f7da05dc4ec857a0e340ece 90a8cd256003d1ef0a989541782c4f148d0c8b6b QAPT User <<EMAIL>> 1750192071 +0200	commit: Add additional comment for field name change
90a8cd256003d1ef0a989541782c4f148d0c8b6b 398a9e17884557ee885454c951375d2f77c0e917 QAPT User <<EMAIL>> 1750201698 +0200	commit: Fix TypeScript implicit any type error in export route
398a9e17884557ee885454c951375d2f77c0e917 dd226e27a665282a72bc919834d6c9a8da733266 QAPT User <<EMAIL>> 1750201900 +0200	commit: Fix export route field reference error
dd226e27a665282a72bc919834d6c9a8da733266 09342b35eb68bb00c63583acc55ac5d8d0107c0a LaxBloxBoy2 <<EMAIL>> 1750203354 +0200	commit: fix: update clerkClient usage for Clerk v6 compatibility
09342b35eb68bb00c63583acc55ac5d8d0107c0a aae761de8b4557f95b9cea60132879e76ce29fa1 LaxBloxBoy2 <<EMAIL>> 1750203664 +0200	commit: fix: remove non-existent subscription relation from database queries
aae761de8b4557f95b9cea60132879e76ce29fa1 a8558a72a0dfffa9828c3d4fe029096cceb231d4 LaxBloxBoy2 <<EMAIL>> 1750207729 +0200	commit: fix: remove remaining subscription include from GET route
a8558a72a0dfffa9828c3d4fe029096cceb231d4 f6c7542d9b1b4fa07703df51bb594c0ca0e75149 LaxBloxBoy2 <<EMAIL>> 1750208404 +0200	commit: fix: improve analytics error handling and add missing favicon
f6c7542d9b1b4fa07703df51bb594c0ca0e75149 e06047c978cb0673e3f6fbe39ea458671dd130fd LaxBloxBoy2 <<EMAIL>> 1750208759 +0200	commit: fix: add null check for analytics in feature flags
e06047c978cb0673e3f6fbe39ea458671dd130fd 1f58c124e028fac1604f8b6e75f28f74fa7e285c LaxBloxBoy2 <<EMAIL>> 1750209478 +0200	commit: fix: add null checks for analytics in webhook handlers
1f58c124e028fac1604f8b6e75f28f74fa7e285c 7f658c407e3b7501f0f7857417485fb8f92798ac LaxBloxBoy2 <<EMAIL>> 1750210621 +0200	commit: fix: add missing database migration for lastSettingsSync field
7f658c407e3b7501f0f7857417485fb8f92798ac 0766a80533288cea6c54df714a1a1438f6b73908 LaxBloxBoy2 <<EMAIL>> 1750251871 +0200	commit: feat: implement secure VSCode extension login flow
0766a80533288cea6c54df714a1a1438f6b73908 2e27fcffb9c49cdbd15a6ca7b5b4d24e36a64540 LaxBloxBoy2 <<EMAIL>> 1750252520 +0200	commit: docs: add comprehensive documentation to VSCode extension login flow
2e27fcffb9c49cdbd15a6ca7b5b4d24e36a64540 3d2c54d42ae07060c615574120ab41f0a3a4a88f LaxBloxBoy2 <<EMAIL>> 1750255495 +0200	commit: fix: merge cron jobs in vercel.json to prevent deployment conflicts
3d2c54d42ae07060c615574120ab41f0a3a4a88f 350d4922841916a12e74375b3a320501c1ce9c65 LaxBloxBoy2 <<EMAIL>> 1750256986 +0200	commit: fix: adjust cron schedule for Vercel Hobby plan limits
350d4922841916a12e74375b3a320501c1ce9c65 1c5037f8299ac7fb5e3b20865de4eb355b63a14b LaxBloxBoy2 <<EMAIL>> 1750257410 +0200	commit: fix: resolve NextRequest.ip TypeScript error
1c5037f8299ac7fb5e3b20865de4eb355b63a14b df78e7d48bf30f4f77a2c6b83967eb0579c24baf LaxBloxBoy2 <<EMAIL>> 1750257727 +0200	commit: fix: resolve Clerk OAuth token TypeScript error
df78e7d48bf30f4f77a2c6b83967eb0579c24baf df4eafd8ee83fe0d8bde2e70b780609d0532c5fd LaxBloxBoy2 <<EMAIL>> 1750260329 +0200	commit: docs: update comment to reflect session token usage
df4eafd8ee83fe0d8bde2e70b780609d0532c5fd ced019f8f6d0e5c18b40546e5c988e85bcae51c2 LaxBloxBoy2 <<EMAIL>> 1750274079 +0200	commit: fix: use correct Clerk sessions.createSession method
ced019f8f6d0e5c18b40546e5c988e85bcae51c2 72902068716f0f0d8ec567f266746495cfa30058 LaxBloxBoy2 <<EMAIL>> 1750274286 +0200	commit: fix: remove expiresInSeconds from Clerk createSession
72902068716f0f0d8ec567f266746495cfa30058 5025198ab8450bc706a3ccd3534027c1e1dd17c3 LaxBloxBoy2 <<EMAIL>> 1750280465 +0200	commit: � Fix extension authentication redirect flow
5025198ab8450bc706a3ccd3534027c1e1dd17c3 9cefd0ef3a21b7b1caaa2f023aef487aa80586dd LaxBloxBoy2 <<EMAIL>> 1750286861 +0200	commit: Fix failing test to unblock deployment
9cefd0ef3a21b7b1caaa2f023aef487aa80586dd 2f76ff33b395325a3f95ee471d5151b41bd76da9 LaxBloxBoy2 <<EMAIL>> 1750287603 +0200	commit: Fix extension URI scheme in authentication flow
2f76ff33b395325a3f95ee471d5151b41bd76da9 62cc7a7b4d5665ebe22ed9bf4d16479546e2a254 LaxBloxBoy2 <<EMAIL>> 1750331726 +0200	commit: Fix extension callback URI path - Change vscode://cubent.cubent/callback to vscode://cubent.cubent/auth/callback
62cc7a7b4d5665ebe22ed9bf4d16479546e2a254 722d8b62382b80e6c81dce45a5d6774cef7cb01e LaxBloxBoy2 <<EMAIL>> 1750332479 +0200	commit: Fix extension auth endpoint to handle Bearer tokens
722d8b62382b80e6c81dce45a5d6774cef7cb01e 193b3c54c1079d4f4130de7aa7685c9c2578c0d5 LaxBloxBoy2 <<EMAIL>> 1750332772 +0200	commit: Add debug logging to extension auth endpoint
193b3c54c1079d4f4130de7aa7685c9c2578c0d5 e80d8be4dc9f5aca8d0220b9ae68c57b10f4a528 LaxBloxBoy2 <<EMAIL>> 1750333024 +0200	commit: Fix token deletion issue - Don't delete pendingLogin immediately, extend expiration instead - This allows extension to use token for both polling and auth endpoints
e80d8be4dc9f5aca8d0220b9ae68c57b10f4a528 2dfb8c8e22e8be6d99734eae31ca3a170e088eb8 LaxBloxBoy2 <<EMAIL>> 1750344257 +0200	commit: Update docs styling and fix deployment issues
2dfb8c8e22e8be6d99734eae31ca3a170e088eb8 1ccd0ef11ad81c54f9ecb5721bd341b32179394a LaxBloxBoy2 <<EMAIL>> 1750344970 +0200	commit: Add comprehensive documentation content
1ccd0ef11ad81c54f9ecb5721bd341b32179394a 0ba8e70a0db24658ecd321ebd5d3f76c9cbaa52b LaxBloxBoy2 <<EMAIL>> 1750345180 +0200	commit: Add comprehensive Models & Pricing documentation
0ba8e70a0db24658ecd321ebd5d3f76c9cbaa52b 48445164607517c02536ecfbed3fe605a80d8715 LaxBloxBoy2 <<EMAIL>> 1750346513 +0200	commit: Fix broken documentation links
48445164607517c02536ecfbed3fe605a80d8715 d8554eea87070fabbaca4627dae4ba0544ccdead LaxBloxBoy2 <<EMAIL>> 1750346997 +0200	commit: � Fix Mintlify deployment - Use docs.json instead of mint.json
d8554eea87070fabbaca4627dae4ba0544ccdead 668759bc49b4b27efd97d352fe0b736d56aea9f8 LaxBloxBoy2 <<EMAIL>> 1750347630 +0200	commit: � Fix Mintlify deployment with proper docs.json
668759bc49b4b27efd97d352fe0b736d56aea9f8 142d9f202e098eaa79ae1cd1d7fa286d9c7e0b15 LaxBloxBoy2 <<EMAIL>> 1750348747 +0200	commit: � Fix docs.json theme validation error
142d9f202e098eaa79ae1cd1d7fa286d9c7e0b15 afd6014c17170e7c26b4b84acb7ed1c4e00c49f0 LaxBloxBoy2 <<EMAIL>> 1750348928 +0200	commit: Fix docs.json theme validation
afd6014c17170e7c26b4b84acb7ed1c4e00c49f0 4346da331e38348e4045904ba0c833ba0717d5b9 LaxBloxBoy2 <<EMAIL>> 1750349095 +0200	commit: � Fix docs.json schema validation errors
4346da331e38348e4045904ba0c833ba0717d5b9 01b055a3cc81be679e89c2af5a16d25fe00906a2 LaxBloxBoy2 <<EMAIL>> 1750349378 +0200	commit: � Clean up old Mintlify starter content
01b055a3cc81be679e89c2af5a16d25fe00906a2 e7ccfa0b99b0cbf1b9d8bf5a5ebdab8484d7fc24 LaxBloxBoy2 <<EMAIL>> 1750350049 +0200	commit: � Fix navigation schema - revert to array format
e7ccfa0b99b0cbf1b9d8bf5a5ebdab8484d7fc24 d468da69dbc30244adc6125b812576bf85ca854d LaxBloxBoy2 <<EMAIL>> 1750350670 +0200	commit: Fix navigation schema format
d468da69dbc30244adc6125b812576bf85ca854d 53ba8c816be9fa2f358375820844d47027b04977 LaxBloxBoy2 <<EMAIL>> 1750354463 +0200	commit: � Improve documentation navigation and styling
53ba8c816be9fa2f358375820844d47027b04977 75c7d1455120f394c86d00724863299c89b1c52c LaxBloxBoy2 <<EMAIL>> 1750354901 +0200	commit: Fix theme validation - use prism theme
75c7d1455120f394c86d00724863299c89b1c52c 86ba12769925f4b900e90e0078ea876d73c0ae4c LaxBloxBoy2 <<EMAIL>> 1750354995 +0200	commit: � Fix theme validation - use mint theme (from deployment error list)
86ba12769925f4b900e90e0078ea876d73c0ae4c c6ee54d17d323c9372695b59cdd05311b7bc4f72 LaxBloxBoy2 <<EMAIL>> 1750355611 +0200	commit: � Split subscription plans into separate page
c6ee54d17d323c9372695b59cdd05311b7bc4f72 44e91f6c785b17e7e57b1589401f00dc89ff174c LaxBloxBoy2 <<EMAIL>> 1750356812 +0200	commit: Fix navigation styling - white text for selected items
44e91f6c785b17e7e57b1589401f00dc89ff174c d5c7f24b19f67af73fe4c0fc5609a4c49f25dce9 LaxBloxBoy2 <<EMAIL>> 1750357047 +0200	commit: � Fix color contrast for light/dark modes
d5c7f24b19f67af73fe4c0fc5609a4c49f25dce9 9997f64dff0c0be304e3e085234e89279538ffd0 LaxBloxBoy2 <<EMAIL>> 1750362091 +0200	commit: � Fix dark mode navigation contrast
9997f64dff0c0be304e3e085234e89279538ffd0 311fc47e252a67d49d999f883a3f22444dc8bda0 LaxBloxBoy2 <<EMAIL>> 1750362280 +0200	commit: � Remove invalid background property from colors
311fc47e252a67d49d999f883a3f22444dc8bda0 a47e64716781aff6b2d74df953c5ed896b055253 LaxBloxBoy2 <<EMAIL>> 1750364761 +0200	commit: Fix Mintlify sidebar selected state visibility issue - changed colors from white/black to proper blue scheme
a47e64716781aff6b2d74df953c5ed896b055253 f0cbe66b0d5f66106f8e670fffada0f2d71a2805 LaxBloxBoy2 <<EMAIL>> 1750365293 +0200	commit: Update Mintlify docs with proper Cubent logo and favicon - replaced Mint Starter Kit branding with Cubent branding
f0cbe66b0d5f66106f8e670fffada0f2d71a2805 10d72efc6cf347faabf6f9f3f763cad62b676f7d LaxBloxBoy2 <<EMAIL>> 1750365422 +0200	commit: Fix duplicate SVG closing tag in dark logo
10d72efc6cf347faabf6f9f3f763cad62b676f7d b185c0b33e702b227869a916a92e72cfbe671118 LaxBloxBoy2 <<EMAIL>> 1750365547 +0200	commit: Force Mintlify rebuild by updating site name
b185c0b33e702b227869a916a92e72cfbe671118 74357a35b8c2ae1f0a161696864a2b716317ff1b LaxBloxBoy2 <<EMAIL>> 1750373900 +0200	commit: Fix logo text rendering by using SVG paths instead of text elements - should now properly display 'Cubent' instead of 'dark logo'
74357a35b8c2ae1f0a161696864a2b716317ff1b 85b0c27d3c8aa48784361462cdebbf2c31609d73 LaxBloxBoy2 <<EMAIL>> 1750374109 +0200	commit: Improve logo spacing and sizing for better visual appearance
85b0c27d3c8aa48784361462cdebbf2c31609d73 e3ea73aa3c3be367d1d905dd3679469482622cc5 LaxBloxBoy2 <<EMAIL>> 1750374339 +0200	commit: Replace complex SVG path text with clean website logo and normal text - now shows proper Cubent logo from website with simple 'Cubent' text
e3ea73aa3c3be367d1d905dd3679469482622cc5 de8d86f31868e04fd6e07151ee982812ca94a3a1 LaxBloxBoy2 <<EMAIL>> 1750374527 +0200	commit: Fix logo size and colors to match website exactly - smaller logo, black on light mode, white on dark mode, proper sizing
de8d86f31868e04fd6e07151ee982812ca94a3a1 a07c81458582990696e2a31c48cdf1abf7e58b81 LaxBloxBoy2 <<EMAIL>> 1750376145 +0200	commit: Fix light mode logo by removing duplicate closing SVG tag that was breaking the logo display
a07c81458582990696e2a31c48cdf1abf7e58b81 c99c190e38a60606d0e979ad7d4a78ba5bd3c7fb LaxBloxBoy2 <<EMAIL>> 1750447224 +0200	commit: feat: implement Cubent Units tracking system
c99c190e38a60606d0e979ad7d4a78ba5bd3c7fb 2eff47211b8bb36ceb807c5210c7a4f6e776cfb7 LaxBloxBoy2 <<EMAIL>> 1750447712 +0200	commit: fix: remove invalid UsageOverview component causing build error
2eff47211b8bb36ceb807c5210c7a4f6e776cfb7 6302ada69b873126f025b6d1637751289045d2c7 LaxBloxBoy2 <<EMAIL>> 1750458084 +0200	commit: Clean schema without token tracking - reverted to original
6302ada69b873126f025b6d1637751289045d2c7 96e12e66c155edb9531c824b5e89167a16774bc7 LaxBloxBoy2 <<EMAIL>> 1750458316 +0200	commit: Remove all usage tracking tables and columns from database
96e12e66c155edb9531c824b5e89167a16774bc7 a581b1469ef71635213345c24931b54000ee3328 LaxBloxBoy2 <<EMAIL>> 1750459581 +0200	commit: Revert database migration - keep clean schema without usage tracking
a581b1469ef71635213345c24931b54000ee3328 7d040f89d901f61e51c0bdf1041edf011ee17854 LaxBloxBoy2 <<EMAIL>> 1750461953 +0200	commit: Fix: Remove cron job to resolve Vercel deployment limit issue
7d040f89d901f61e51c0bdf1041edf011ee17854 b91a8ad2755b13357e0e0bbb77fa8ef072b6c47c LaxBloxBoy2 <<EMAIL>> 1750505045 +0200	commit: Add test file
b91a8ad2755b13357e0e0bbb77fa8ef072b6c47c eb2b6cc69d6d4c696aee4d5c054ee8bb58cd2885 LaxBloxBoy2 <<EMAIL>> 1750505074 +0200	pull origin main: Merge made by the 'ort' strategy.
eb2b6cc69d6d4c696aee4d5c054ee8bb58cd2885 5a52ca5d9b394b3bc88e6c790a3683aade94cd77 LaxBloxBoy2 <<EMAIL>> 1750505438 +0200	commit: Add test button to VS Code Extension section
5a52ca5d9b394b3bc88e6c790a3683aade94cd77 7f600ea74311833c5ca4ff8145e331c58e67ffc4 LaxBloxBoy2 <<EMAIL>> 1750508393 +0200	commit: Implement comprehensive Cubent Units token usage system
7f600ea74311833c5ca4ff8145e331c58e67ffc4 5898ea88df2fe01f66ff44a319477ba89433c392 LaxBloxBoy2 <<EMAIL>> 1750508777 +0200	commit: Fix TypeScript errors in token usage reset endpoints
5898ea88df2fe01f66ff44a319477ba89433c392 4be27bfbbf1185ba924ef47cd8767fdcf6d45a5a LaxBloxBoy2 <<EMAIL>> 1750509032 +0200	commit: Fix Prisma groupBy TypeScript error in getUserModelUsage
4be27bfbbf1185ba924ef47cd8767fdcf6d45a5a a4c793fa17bbc5b276c0fdd308b030ed69da2253 LaxBloxBoy2 <<EMAIL>> 1750550555 +0200	commit: fix: automatically create user profiles for social login users
a4c793fa17bbc5b276c0fdd308b030ed69da2253 28dc06a1eff2937c449d35d85b0157725b09568c LaxBloxBoy2 <<EMAIL>> 1750603051 +0200	commit: feat: add Cubent Units Usage analytics to webapp
28dc06a1eff2937c449d35d85b0157725b09568c b8fa4ff681d94725fd6a5de494a8ec1518ac63a8 LaxBloxBoy2 <<EMAIL>> 1750604819 +0200	commit: feat: rebuild usage analytics with ChatGPT-like design and Cubent Units tracking
b8fa4ff681d94725fd6a5de494a8ec1518ac63a8 d12974c67869b49f6d25477e27ebb5fd990054b2 LaxBloxBoy2 <<EMAIL>> 1750607954 +0200	commit: fix: redesign usage analytics to match sidebar design principles and add demo data
d12974c67869b49f6d25477e27ebb5fd990054b2 c25a4d650dd2ccef3ba1f35f210486e282eb834c LaxBloxBoy2 <<EMAIL>> 1750611014 +0200	commit: fix: remove sample data and sync webapp with extension usage tracking
c25a4d650dd2ccef3ba1f35f210486e282eb834c 03113bcc19cc13686e2d5714e5c1b4e1a71a627a LaxBloxBoy2 <<EMAIL>> 1750615668 +0200	commit: Add debug pages to check usage data in database
03113bcc19cc13686e2d5714e5c1b4e1a71a627a b60b1ce7f77c112bda3f325d7726eaa68ea05f30 LaxBloxBoy2 <<EMAIL>> 1750615807 +0200	commit: Fix extension authentication for usage tracking API endpoints
b60b1ce7f77c112bda3f325d7726eaa68ea05f30 e8c7ab6cb3811e3f184a1d42ebe749fe57a8e813 LaxBloxBoy2 <<EMAIL>> 1750616940 +0200	commit: Fix extension authentication for usage API endpoints
e8c7ab6cb3811e3f184a1d42ebe749fe57a8e813 c809bda5aebc6b5a78f982af5803bacda76640fc LaxBloxBoy2 <<EMAIL>> 1750633209 +0200	commit: Fix extension usage tracking authentication - support direct Clerk JWT validation
c809bda5aebc6b5a78f982af5803bacda76640fc 3cb803ffb775fb1c602afa128df3033154c97896 LaxBloxBoy2 <<EMAIL>> 1750633423 +0200	commit: Fix duplicate totalMessages variable declaration in stats route
3cb803ffb775fb1c602afa128df3033154c97896 c3cd866f5111d9e39dcacbe74b325f5f5cdfef58 LaxBloxBoy2 <<EMAIL>> 1750633650 +0200	commit: Fix missing clerkClient import in usage route
c3cd866f5111d9e39dcacbe74b325f5f5cdfef58 fda4310febcc361bf52836c4fa30b1937423eb0b LaxBloxBoy2 <<EMAIL>> 1750634278 +0200	commit: Redesign Cubent Units Usage page with sidebar-style layout and enhanced chart visualization
fda4310febcc361bf52836c4fa30b1937423eb0b bc7a143cbb0195dcdc66a85c4079e9529028f82b LaxBloxBoy2 <<EMAIL>> 1750634747 +0200	commit: Redesign Cubent Units Usage page with elegant, comprehensive, and simple design
bc7a143cbb0195dcdc66a85c4079e9529028f82b b48b8809209ccc1e7a22680e3b61e6879394a46c LaxBloxBoy2 <<EMAIL>> 1750635307 +0200	commit: Redesign Cubent Units Usage page with Xenith-inspired clean dashboard design
b48b8809209ccc1e7a22680e3b61e6879394a46c 5310f5fdd98e481491ec7c5e92e5a1e664cf379a LaxBloxBoy2 <<EMAIL>> 1750635769 +0200	commit: Fix dark mode colors and implement smooth line chart design
5310f5fdd98e481491ec7c5e92e5a1e664cf379a b1561df147d5174a3aaf60a764a65472827630c7 LaxBloxBoy2 <<EMAIL>> 1750698738 +0200	commit: Update usage analytics and sidebar to use shadcn-admin design patterns
b1561df147d5174a3aaf60a764a65472827630c7 20571c06c9cbf5a2cf437417009f4c41076e0b90 LaxBloxBoy2 <<EMAIL>> 1750699703 +0200	commit: Integrate Clerk authentication into website header
20571c06c9cbf5a2cf437417009f4c41076e0b90 0c104df35761608ecb1b5fb0533b8e72eb2b8eed LaxBloxBoy2 <<EMAIL>> 1750699978 +0200	commit: Simplify website-to-app integration by redirecting to app auth pages
0c104df35761608ecb1b5fb0533b8e72eb2b8eed 6f797fc800dc908522a4acf5c459fd75c1334092 LaxBloxBoy2 <<EMAIL>> 1750700507 +0200	commit: Add cross-domain authentication detection between website and app
6f797fc800dc908522a4acf5c459fd75c1334092 38561139736e6633349bb834da8707e729d74ee4 LaxBloxBoy2 <<EMAIL>> 1750700903 +0200	commit: Fix CORS configuration for cross-domain auth detection
38561139736e6633349bb834da8707e729d74ee4 f0eb45f179e2796915dadb4684db2de8c97658c8 LaxBloxBoy2 <<EMAIL>> 1750701017 +0200	commit: Implement URL-based auth sync between app and website
f0eb45f179e2796915dadb4684db2de8c97658c8 77e67b3aa9083f3c9128a1a52f7160a86af1db9e LaxBloxBoy2 <<EMAIL>> 1750701448 +0200	commit: Fix TypeScript error in CORS headers
77e67b3aa9083f3c9128a1a52f7160a86af1db9e 78acbe77f0ca7c4ef1182fdee540ce7d66f9b406 LaxBloxBoy2 <<EMAIL>> 1750701950 +0200	commit: Implement automatic cross-domain authentication detection
78acbe77f0ca7c4ef1182fdee540ce7d66f9b406 c35684e57c5048eb8fb6dbf6c683f17c93a04f54 LaxBloxBoy2 <<EMAIL>> 1750702328 +0200	commit: Add debugging and improve cross-domain auth detection
c35684e57c5048eb8fb6dbf6c683f17c93a04f54 13f683f938d502aa706cd45ac364ca432f1ba3bc LaxBloxBoy2 <<EMAIL>> 1750705742 +0200	commit: Add detailed debugging for auth state and UI rendering
13f683f938d502aa706cd45ac364ca432f1ba3bc 6d220dcfeb8b59da34870adc676bfcb7372eb529 LaxBloxBoy2 <<EMAIL>> 1750706106 +0200	commit: Fix cross-domain cookie authentication issue
6d220dcfeb8b59da34870adc676bfcb7372eb529 3ec9e5a69a79bdc494ef8dce6f15acb9c8003279 LaxBloxBoy2 <<EMAIL>> 1750706649 +0200	commit: Implement token-based cross-domain authentication
3ec9e5a69a79bdc494ef8dce6f15acb9c8003279 d841ba69b1f959328d69309fe6761f3fbabcaffc LaxBloxBoy2 <<EMAIL>> 1750707053 +0200	commit: Add jose dependency for JWT token handling
d841ba69b1f959328d69309fe6761f3fbabcaffc 820232666e5ac18d8baa301ee56eb60f3517c78f LaxBloxBoy2 <<EMAIL>> 1750708600 +0200	commit: Add cross-domain authentication detection to website header
820232666e5ac18d8baa301ee56eb60f3517c78f 427ae7abbeb7b405cf708e2c0b997a61da875c27 LaxBloxBoy2 <<EMAIL>> 1750708921 +0200	commit: Fix TypeScript error in auth status API
427ae7abbeb7b405cf708e2c0b997a61da875c27 c53a55455668448a1ad2fec2218f1473eff9bb62 LaxBloxBoy2 <<EMAIL>> 1750709341 +0200	commit: Fix deployment to build web app instead of app
c53a55455668448a1ad2fec2218f1473eff9bb62 f1efd8c19755321e98211684350334c42e0028ac LaxBloxBoy2 <<EMAIL>> 1750709609 +0200	commit: Add debugging and manual auth check button
f1efd8c19755321e98211684350334c42e0028ac c5360217e83343ee020696aa92d82cdd5fd66857 LaxBloxBoy2 <<EMAIL>> 1750709898 +0200	commit: Fix CORS issues and improve auth detection
c5360217e83343ee020696aa92d82cdd5fd66857 887994e7063df50dbb0d53d089ea79484f91bf4d LaxBloxBoy2 <<EMAIL>> 1750710241 +0200	commit: Fix CORS credentials issue for cross-domain auth
887994e7063df50dbb0d53d089ea79484f91bf4d b88a78794ff41c75b9dc586021e222c8f617b599 LaxBloxBoy2 <<EMAIL>> 1750710507 +0200	commit: Fix auth detection logic and add detailed logging
b88a78794ff41c75b9dc586021e222c8f617b599 d35c76bf16191270a60c0bbeaf840367bbd67245 LaxBloxBoy2 <<EMAIL>> 1750710674 +0200	commit: Clean up auth detection - remove logging and debug button
d35c76bf16191270a60c0bbeaf840367bbd67245 69a7925db47d108d5ad13bba9155f60e9123e9ff LaxBloxBoy2 <<EMAIL>> 1750711663 +0200	commit: Implement cross-domain authentication for website header
69a7925db47d108d5ad13bba9155f60e9123e9ff 30b48d76eb74d82d3a5ab2d77dee6aedb5715b77 LaxBloxBoy2 <<EMAIL>> 1750719405 +0200	commit: Trigger deployment for cross-domain auth feature
30b48d76eb74d82d3a5ab2d77dee6aedb5715b77 90953632c10019aeb5eb79650f3538961f8814be LaxBloxBoy2 <<EMAIL>> 1750719677 +0200	commit: Force deployment - cross-domain auth ready
90953632c10019aeb5eb79650f3538961f8814be dc847f960eb31f4caad797e1d84e5ab94858a7c2 LaxBloxBoy2 <<EMAIL>> 1750719893 +0200	commit: Fix TypeScript error in cross-domain auth hook
dc847f960eb31f4caad797e1d84e5ab94858a7c2 bf6af9b24a6d5ce3291eca300090591fc0fbf626 LaxBloxBoy2 <<EMAIL>> 1750720231 +0200	commit: Fix CORS and production URL configuration for cross-domain auth
bf6af9b24a6d5ce3291eca300090591fc0fbf626 264062c0b65189e692e50d624ce73bc16d0311e0 LaxBloxBoy2 <<EMAIL>> 1750720367 +0200	commit: Add debugging logs to cross-domain auth hook
264062c0b65189e692e50d624ce73bc16d0311e0 415b3af3a54554a5fd9db3e20c3a3e6cc45c9c9a LaxBloxBoy2 <<EMAIL>> 1750722122 +0200	commit: Fix usage chart rendering issues
415b3af3a54554a5fd9db3e20c3a3e6cc45c9c9a 2598a65d67911059aa1e6f0cacc94971e1408c1a LaxBloxBoy2 <<EMAIL>> 1750722433 +0200	commit: Reduce spacing between header and announcement banner
2598a65d67911059aa1e6f0cacc94971e1408c1a 4c4e47a115d7ffb00b4ea93c879310f85992ada5 LaxBloxBoy2 <<EMAIL>> 1750883929 +0200	commit: feat: implement authentication header for cubent.dev website
4c4e47a115d7ffb00b4ea93c879310f85992ada5 01b6b4ffd31c7fac09d2ef3074fdee83b3cf505a LaxBloxBoy2 <<EMAIL>> 1750884114 +0200	commit: fix: add @clerk/nextjs dependency to web app
01b6b4ffd31c7fac09d2ef3074fdee83b3cf505a cafe5e546ee5de14fa11ba5201e94b2221614a50 LaxBloxBoy2 <<EMAIL>> 1750895161 +0200	commit: fix: correct TypeScript types for Clerk user in UserProfile component
cafe5e546ee5de14fa11ba5201e94b2221614a50 1a7a75782c0ac1b94091abc53ec51956599539df LaxBloxBoy2 <<EMAIL>> 1750895300 +0200	commit: fix: import UserResource from @clerk/nextjs instead of @clerk/types
1a7a75782c0ac1b94091abc53ec51956599539df 12105eafd81f469f740abfb7d3e6635627e9404e LaxBloxBoy2 <<EMAIL>> 1750895487 +0200	commit: fix: use correct TypeScript type for Clerk user object
12105eafd81f469f740abfb7d3e6635627e9404e ae7c6a521422fefdc28581c9d8df6c400615d8f3 LaxBloxBoy2 <<EMAIL>> 1750895917 +0200	commit: fix: implement cross-domain authentication and correct sign-in URLs
ae7c6a521422fefdc28581c9d8df6c400615d8f3 3f158a825528b04b4e6c9d9794edaf7788443243 LaxBloxBoy2 <<EMAIL>> 1750896077 +0200	commit: fix: correct import path for useAuthStatus hook
3f158a825528b04b4e6c9d9794edaf7788443243 b14aa943ac9dea34a47cb476188fa3665f59a11c LaxBloxBoy2 <<EMAIL>> 1750896487 +0200	commit: fix: implement proper cross-domain authentication using postMessage
b14aa943ac9dea34a47cb476188fa3665f59a11c e299e15d09ff6c346455340dc7dd6fb4b59bd1a0 LaxBloxBoy2 <<EMAIL>> 1750896797 +0200	commit: fix: add @clerk/nextjs dependency to app package
e299e15d09ff6c346455340dc7dd6fb4b59bd1a0 21fea7370a3d64c46b28dae8f7c5b16bf2415a07 LaxBloxBoy2 <<EMAIL>> 1750897195 +0200	commit: fix: disable cross-domain authentication for now
21fea7370a3d64c46b28dae8f7c5b16bf2415a07 917bd560d0ae92e7e816867f9796af177e25efa7 LaxBloxBoy2 <<EMAIL>> 1750897713 +0200	commit: feat: implement proper cross-subdomain authentication with Clerk
917bd560d0ae92e7e816867f9796af177e25efa7 61b5f46b1cb2d0cbe249349e03c3eec20d062351 LaxBloxBoy2 <<EMAIL>> 1750898263 +0200	commit: debug: add logging and CORS headers for cross-domain auth
61b5f46b1cb2d0cbe249349e03c3eec20d062351 94250e1416aa1ae7c2039ac95980c0a6ac79857d LaxBloxBoy2 <<EMAIL>> 1750898896 +0200	commit: feat: implement custom cross-domain authentication token system
94250e1416aa1ae7c2039ac95980c0a6ac79857d 77f30f6c328b1816093fb47e19e337cfce605d64 LaxBloxBoy2 <<EMAIL>> 1750899367 +0200	commit: fix: URL decode auth token before base64 decoding
77f30f6c328b1816093fb47e19e337cfce605d64 45ff22dd4b1b1a43f09e84030986a4c3bf1b3151 LaxBloxBoy2 <<EMAIL>> 1750899940 +0200	commit: feat: implement cross-domain logout functionality
45ff22dd4b1b1a43f09e84030986a4c3bf1b3151 f23699bc307251e01a09606ceeb9fa8560bc8c21 LaxBloxBoy2 <<EMAIL>> 1750947231 +0200	commit: Add comment to layout component
f23699bc307251e01a09606ceeb9fa8560bc8c21 0f3b5faf922629f4117ee6d91e6e047e848b638c LaxBloxBoy2 <<EMAIL>> 1750948002 +0200	commit: Fix Clerk deprecated props and PostHog configuration
0f3b5faf922629f4117ee6d91e6e047e848b638c 40b27400927fe4f94d80d25ac2eb6471b00a20de LaxBloxBoy2 <<EMAIL>> 1750949180 +0200	commit: Fix Prisma unique constraint errors on user creation
40b27400927fe4f94d80d25ac2eb6471b00a20de d47bfa712d3332f4d732ff519635713d30357e74 LaxBloxBoy2 <<EMAIL>> 1750949443 +0200	commit: Fix TypeScript error in user upsert - use correct field names
d47bfa712d3332f4d732ff519635713d30357e74 839acf1e17840bfc4109081bc60ba41ed825faae LaxBloxBoy2 <<EMAIL>> 1750955040 +0200	commit: Fix extension authentication flow
839acf1e17840bfc4109081bc60ba41ed825faae 5b1e386d736525e1be6b09ba8055534ffaf5ed4e LaxBloxBoy2 <<EMAIL>> 1750956071 +0200	commit: Fix TypeScript build error by regenerating Prisma client
5b1e386d736525e1be6b09ba8055534ffaf5ed4e df76e4038a88e293b338023c761773d2be1515fd LaxBloxBoy2 <<EMAIL>> 1750957113 +0200	commit: Fix critical usage analytics authentication bug
df76e4038a88e293b338023c761773d2be1515fd 96ce9b8fb6258a13e26dc680e9924a6be3e3807e LaxBloxBoy2 <<EMAIL>> 1750963299 +0200	commit: Fix usage tracking authentication and improve error handling
96ce9b8fb6258a13e26dc680e9924a6be3e3807e 0949add177331bccfa5f9060240645560f6128a2 LaxBloxBoy2 <<EMAIL>> 1750963829 +0200	commit: Re-apply critical usage analytics authentication fix
0949add177331bccfa5f9060240645560f6128a2 0f9759eb4b9ed37460c6035d68813777dee25784 LaxBloxBoy2 <<EMAIL>> 1750987638 +0200	commit: feat: implement 4-bento Cubent extension showcase
0f9759eb4b9ed37460c6035d68813777dee25784 89c2605ec01b578ef166754d43c21878b9fdb273 LaxBloxBoy2 <<EMAIL>> 1750987849 +0200	commit: fix: escape JSX syntax for terminal prompt character
89c2605ec01b578ef166754d43c21878b9fdb273 5b1fc87a140ec0c6b15ad60faddfa963493a6423 LaxBloxBoy2 <<EMAIL>> 1750988014 +0200	commit: fix: escape JSX syntax for Java code braces
5b1fc87a140ec0c6b15ad60faddfa963493a6423 131597d967d068fb33a51aca4057b09b96a7bc9e LaxBloxBoy2 <<EMAIL>> 1750988310 +0200	commit: feat: add CubentBento component to homepage
131597d967d068fb33a51aca4057b09b96a7bc9e 913d2a73d1f91cdfee5f90a4e858a168f3bf766b LaxBloxBoy2 <<EMAIL>> 1750988735 +0200	commit: feat: add Cubent extension feature images with descriptions
913d2a73d1f91cdfee5f90a4e858a168f3bf766b 0b181b0f9fad973c4cd4dfff58e8079d89bbcbc3 LaxBloxBoy2 <<EMAIL>> 1750989427 +0200	commit: Style feature images with squared aspect ratio, rounded corners, and dark shadows
0b181b0f9fad973c4cd4dfff58e8079d89bbcbc3 220c4c9744fd8a09f0d5eb14ff389b08f314570c LaxBloxBoy2 <<EMAIL>> 1750989745 +0200	commit: Fix feature image sizes - reduce from aspect-square to h-48 for better proportions
220c4c9744fd8a09f0d5eb14ff389b08f314570c 9134237fbfb26fca83e9b6fc480e743b1f5d65be LaxBloxBoy2 <<EMAIL>> 1751033930 +0200	commit: feat: update features section with modern squared dark design
9134237fbfb26fca83e9b6fc480e743b1f5d65be 2bb86ce7ae4d80012c9a6675aa754e0aa616ae53 LaxBloxBoy2 <<EMAIL>> 1751034400 +0200	commit: feat: improve features section with darker background and squared cards
2bb86ce7ae4d80012c9a6675aa754e0aa616ae53 221bb368c47ef00eda6c68ad568c8fa6dc214b22 LaxBloxBoy2 <<EMAIL>> 1751034773 +0200	commit: revert: restore features section to original design
221bb368c47ef00eda6c68ad568c8fa6dc214b22 6c510001a23cb116c8e4e238f9a25aed82af3aa1 LaxBloxBoy2 <<EMAIL>> 1751035977 +0200	commit: feat: redesign homepage with modern Unkey-inspired layout
6c510001a23cb116c8e4e238f9a25aed82af3aa1 998dd782117374555f8a1602986517dece62fc77 LaxBloxBoy2 <<EMAIL>> 1751036992 +0200	commit: Complete www homepage integration with all components and styling
998dd782117374555f8a1602986517dece62fc77 dd28323739644e9ee51ae55ba97b14b40175b16f LaxBloxBoy2 <<EMAIL>> 1751037251 +0200	commit: Add missing UI components and dependencies for www homepage integration
dd28323739644e9ee51ae55ba97b14b40175b16f 2cbea4324d1440c436ac8fa9c9468e0db231a112 LaxBloxBoy2 <<EMAIL>> 1751037486 +0200	commit: Add remaining missing components for www homepage build
2cbea4324d1440c436ac8fa9c9468e0db231a112 43555ec5506e0f4fe305783f67422b9bc413e2b0 LaxBloxBoy2 <<EMAIL>> 1751037753 +0200	commit: Add missing images and hashed-keys component for www homepage
43555ec5506e0f4fe305783f67422b9bc413e2b0 482c888ed9212b429418535bc52468fd42210836 LaxBloxBoy2 <<EMAIL>> 1751038004 +0200	commit: Add missing JavaIcon and UsageSparkles components to fix build errors
482c888ed9212b429418535bc52468fd42210836 66eb6aa4d0073dae3d669eeca962068991d81edd LaxBloxBoy2 <<EMAIL>> 1751038212 +0200	commit: Add missing @radix-ui/react-tabs dependency for code-examples component
66eb6aa4d0073dae3d669eeca962068991d81edd 765d62b430dd0fcf091597b2720bfede0fc529a5 LaxBloxBoy2 <<EMAIL>> 1751038391 +0200	commit: Add missing prism-react-renderer dependency for syntax highlighting
765d62b430dd0fcf091597b2720bfede0fc529a5 ea1d19f47cdf37ba7772f82ec595b715bf5db8ed LaxBloxBoy2 <<EMAIL>> 1751038945 +0200	commit: Fix TypeScript errors: update component props and remove invalid delay props from MeteorLines
ea1d19f47cdf37ba7772f82ec595b715bf5db8ed 5a6544ea65dc2e62b28cd274611058395fa4f6e7 LaxBloxBoy2 <<EMAIL>> 1751039137 +0200	commit: Fix Framer Motion easing type error in hero component
5a6544ea65dc2e62b28cd274611058395fa4f6e7 0193ccf2e534c4a57e6c2251757f7d3315f93ade LaxBloxBoy2 <<EMAIL>> 1751039289 +0200	commit: Remove problematic ease property from Framer Motion transition to fix TypeScript error
0193ccf2e534c4a57e6c2251757f7d3315f93ade 06b5fc4fc7be041a49e9cd3c7afe12666c9e2475 LaxBloxBoy2 <<EMAIL>> 1751041750 +0200	commit: Fix MeteorLines usage in open-source component: remove invalid delay props and fix import path
06b5fc4fc7be041a49e9cd3c7afe12666c9e2475 e9515e0f81ebb6edaccd09fa9de9e3ecbcf527d5 LaxBloxBoy2 <<EMAIL>> 1751041926 +0200	commit: Fix AnimatedList usage in usage-bento: convert children to items prop format
e9515e0f81ebb6edaccd09fa9de9e3ecbcf527d5 dda2f27f0f039dfd8c9a38ba82622d297f32c06b LaxBloxBoy2 <<EMAIL>> 1751042802 +0200	commit: Add missing clsx and tailwind-merge dependencies for utils.ts
dda2f27f0f039dfd8c9a38ba82622d297f32c06b e19b794bef6dba252a2bb893c40cabd6aae2c88a LaxBloxBoy2 <<EMAIL>> 1751043119 +0200	commit: Add safety checks to AnimatedList component to prevent undefined array errors
e19b794bef6dba252a2bb893c40cabd6aae2c88a 33309b1b9fff8bbc6348747622acc75ae0c23cae LaxBloxBoy2 <<EMAIL>> 1751044052 +0200	commit: Fix undefined array error in footer component by adding null safety checks
33309b1b9fff8bbc6348747622acc75ae0c23cae ae1f7a3cf50c74df48db9f1ada20de5b6444048a LaxBloxBoy2 <<EMAIL>> 1751050065 +0200	commit: Replace homepage with hero section from www template - clean minimal design with just the hero
ae1f7a3cf50c74df48db9f1ada20de5b6444048a c235a2dad0090de47416d93cf43136265c11f752 LaxBloxBoy2 <<EMAIL>> 1751050565 +0200	commit: Add missing lib/utils.ts file to fix build errors
c235a2dad0090de47416d93cf43136265c11f752 0841c003013e23705f3fd71ef57420453ae15078 LaxBloxBoy2 <<EMAIL>> 1751050715 +0200	commit: Fix mainboard.svg import path in hero component
0841c003013e23705f3fd71ef57420453ae15078 f0d87059d7a426c71dd9d94316be9ea46cc1ea30 LaxBloxBoy2 <<EMAIL>> 1751080192 +0200	commit: docs: add documentation comment to README
f0d87059d7a426c71dd9d94316be9ea46cc1ea30 c6029d6e32ea97fa2366f76987613a1535bc31db LaxBloxBoy2 <<EMAIL>> 1751080559 +0200	commit: fix: remove deleted hero components and clean up imports
c6029d6e32ea97fa2366f76987613a1535bc31db 51322e3c6c445d9983c32be08eb180068ea9458d LaxBloxBoy2 <<EMAIL>> 1751080808 +0200	commit: fix: correct import path for cn utility in hero SVG component
51322e3c6c445d9983c32be08eb180068ea9458d eadfb03cebfc230196a2d7d604de5342864300b2 LaxBloxBoy2 <<EMAIL>> 1751200448 +0200	commit: feat: Replace confusing line chart with functional day-by-day bar chart
eadfb03cebfc230196a2d7d604de5342864300b2 104b4146e15612aef38db25ea386d0c35707cccd LaxBloxBoy2 <<EMAIL>> 1751200619 +0200	commit: fix: Reduce middleware bundle size to fix Vercel deployment
104b4146e15612aef38db25ea386d0c35707cccd 23765dab673c50bb17380e1a66c5e66564c6b3f2 LaxBloxBoy2 <<EMAIL>> 1751200893 +0200	commit: fix: Add recharts dependency to fix build error
23765dab673c50bb17380e1a66c5e66564c6b3f2 546ccca2792b359c4fd172d6ec24b96eb728b3a3 LaxBloxBoy2 <<EMAIL>> 1751201764 +0200	commit: CRITICAL FIX: Stop deleting authentication tokens after 2 hours
546ccca2792b359c4fd172d6ec24b96eb728b3a3 9ecd55b85678381051c442ac458fbacb3f5db6d0 LaxBloxBoy2 <<EMAIL>> 1751202213 +0200	commit: fix: Extend cross-domain cookie expiration from 7 to 30 days
9ecd55b85678381051c442ac458fbacb3f5db6d0 7ef7e46c0b94c380c6a96beee53acdce0eb8602e LaxBloxBoy2 <<EMAIL>> 1751204408 +0200	commit: feat: Enhance charts and add dynamic grid background
7ef7e46c0b94c380c6a96beee53acdce0eb8602e 3fb21b208dff0456b7fc64771d003d6c83cd2868 LaxBloxBoy2 <<EMAIL>> 1751204711 +0200	commit: fix: Revert chart back to working bar chart
3fb21b208dff0456b7fc64771d003d6c83cd2868 a9ea33852ef6726971c4c60d64fd4d627e5bd737 LaxBloxBoy2 <<EMAIL>> 1751204987 +0200	commit: feat: Add EXACT grid lines as drawn
a9ea33852ef6726971c4c60d64fd4d627e5bd737 e0b5106bc845a2bc1a0509c1ae34b5cddb18868f LaxBloxBoy2 <<EMAIL>> 1751205772 +0200	commit: feat: Add EXACT dashed grid lines from HTML example
e0b5106bc845a2bc1a0509c1ae34b5cddb18868f 71105b1954f7393c7304fd2096af7424d77d7fd2 LaxBloxBoy2 <<EMAIL>> 1751206030 +0200	commit: fix: Move grid lines to correct Hero component
71105b1954f7393c7304fd2096af7424d77d7fd2 1a3a2dfac785353f2225c5504954e30025d30f25 LaxBloxBoy2 <<EMAIL>> 1751206840 +0200	commit: feat: Add additional bottom horizontal grid line to close the grid
1a3a2dfac785353f2225c5504954e30025d30f25 b490484281db535edfb7371d54f986c96754a9d8 LaxBloxBoy2 <<EMAIL>> 1751206925 +0200	commit: fix: Reposition grid lines to avoid text conflicts
b490484281db535edfb7371d54f986c96754a9d8 5840a3d1a7b688151f486d34148d63e17723b6d6 LaxBloxBoy2 <<EMAIL>> 1751244468 +0200	commit: Add early access banner to header and update community gradient styling
5840a3d1a7b688151f486d34148d63e17723b6d6 a7cdd75cc7c9b2c0b9978f4c0c3913d5270967d9 LaxBloxBoy2 <<EMAIL>> 1751290535 +0200	commit: Improve authentication layout with pure white modals and dark background
a7cdd75cc7c9b2c0b9978f4c0c3913d5270967d9 59087b9f6036cb967f21ec62292bed72cb439ac9 LaxBloxBoy2 <<EMAIL>> 1751291101 +0200	commit: Fix Clerk authentication modal rendering issue
59087b9f6036cb967f21ec62292bed72cb439ac9 a7496106163135a6dffe011ee40c8acd18e88216 LaxBloxBoy2 <<EMAIL>> 1751291266 +0200	commit: Fix failing sign-up test that was blocking deployment
a7496106163135a6dffe011ee40c8acd18e88216 a14e4395024afe722ca99a03db72c62394bc7904 LaxBloxBoy2 <<EMAIL>> 1751291688 +0200	commit: Fix Clerk modal styling conflicts
a14e4395024afe722ca99a03db72c62394bc7904 af82544bf23246d90bf05f8a2c93263bdaf36d97 LaxBloxBoy2 <<EMAIL>> 1751292116 +0200	commit: Add extensive debugging to SignIn component to diagnose rendering issue
af82544bf23246d90bf05f8a2c93263bdaf36d97 fff81877371678fd65fd84ca885fb81b8d930294 LaxBloxBoy2 <<EMAIL>> 1751292663 +0200	commit: Fix Clerk CSS variable error that was preventing modal from rendering
fff81877371678fd65fd84ca885fb81b8d930294 53556e82eb9103083d34c49c210ad02bb580ba0b LaxBloxBoy2 <<EMAIL>> 1751293461 +0200	commit: Fix Clerk CSS variable error that was preventing modal from rendering
53556e82eb9103083d34c49c210ad02bb580ba0b b4e408c499049ef12ee3f41c4261cf243137a995 LaxBloxBoy2 <<EMAIL>> 1751295071 +0200	commit: Improve auth modal layout: move title and privacy/terms outside modal
b4e408c499049ef12ee3f41c4261cf243137a995 689aa1172147abd269afd315b772d1d35f288003 LaxBloxBoy2 <<EMAIL>> 1751295726 +0200	commit: Fix auth modal: properly hide internal title/footer and improve centering
689aa1172147abd269afd315b772d1d35f288003 889c460d493b2d3b1739510e4e10df00b0540fee LaxBloxBoy2 <<EMAIL>> 1751296109 +0200	commit: Fix build error: remove duplicate card and rootBox properties
889c460d493b2d3b1739510e4e10df00b0540fee 4df093c1d9553fc124e3b4c322932e633545b72b LaxBloxBoy2 <<EMAIL>> 1751296796 +0200	commit: Aggressively hide all Clerk internal elements
4df093c1d9553fc124e3b4c322932e633545b72b c84dfc781afcdaba2e42f574d05e661fc2ee5e73 LaxBloxBoy2 <<EMAIL>> 1751297229 +0200	commit: Fix build error and improve Clerk element hiding
c84dfc781afcdaba2e42f574d05e661fc2ee5e73 6ed50d80d4bb0148201b41744dcd23d687063720 LaxBloxBoy2 <<EMAIL>> 1751297744 +0200	commit: Fix modal transparency and make titles white
6ed50d80d4bb0148201b41744dcd23d687063720 ea69f04732bc088ef4d14ae2b29071e7b3a35d48 LaxBloxBoy2 <<EMAIL>> 1751298567 +0200	commit: Fix missing Continue button and Sign up link
ea69f04732bc088ef4d14ae2b29071e7b3a35d48 c2c9cc1f32d3fece85d4e970c319eb30ff4d9701 LaxBloxBoy2 <<EMAIL>> 1751298919 +0200	commit: Fix modal styling issues: remove internal shadows, round corners, orange button
c2c9cc1f32d3fece85d4e970c319eb30ff4d9701 ff70be95677add4af82787cfc3db0fe082400cbb LaxBloxBoy2 <<EMAIL>> 1751299156 +0200	commit: Fix TypeScript error: remove duplicate formButtonPrimary property
ff70be95677add4af82787cfc3db0fe082400cbb 67392368a31dbb90f225de2773bc540c54ac8e83 LaxBloxBoy2 <<EMAIL>> 1751299628 +0200	commit: 🎨 Major UI improvements: refined modal design and creative welcome text
67392368a31dbb90f225de2773bc540c54ac8e83 24265566ecae0763256d1797818ced2b009b9fa0 LaxBloxBoy2 <<EMAIL>> 1751300190 +0200	commit: 🔧 CRITICAL FIXES: Modal rounding and button styling
24265566ecae0763256d1797818ced2b009b9fa0 342044adb0f4a5127e149ae5c5c27b8d0761a240 LaxBloxBoy2 <<EMAIL>> 1751300420 +0200	commit: 🚨 HOTFIX: Remove duplicate formFieldInput property causing build failure
342044adb0f4a5127e149ae5c5c27b8d0761a240 c62322b23fbf33c9206be18dde491adb67751003 LaxBloxBoy2 <<EMAIL>> 1751300878 +0200	commit: 🎨 Enhanced CSS: Force grey backgrounds on email input and Google button
c62322b23fbf33c9206be18dde491adb67751003 8eb697eae158244ed13ae84f0521ad7933139ec0 LaxBloxBoy2 <<EMAIL>> 1751306995 +0200	commit: 🔧 FIXES: Google icon visibility + tighter spacing
8eb697eae158244ed13ae84f0521ad7933139ec0 5df8ee4704e005f50f380cd0391c3abab4e64a3a LaxBloxBoy2 <<EMAIL>> 1751307524 +0200	commit: 🔧 AGGRESSIVE FIXES: Google icon + vertical spacing
5df8ee4704e005f50f380cd0391c3abab4e64a3a be4cc2d764e6ba9843f3b1a91117b1e421f66b15 LaxBloxBoy2 <<EMAIL>> 1751308155 +0200	commit: 🔧 FIX: Round the actual modal content box
be4cc2d764e6ba9843f3b1a91117b1e421f66b15 e7ea01e580ec39a888f37fd576a8aca95270761c LaxBloxBoy2 <<EMAIL>> 1751308521 +0200	commit: 🔧 HOTFIX: Remove duplicate object properties
e7ea01e580ec39a888f37fd576a8aca95270761c e6d873e8c2d59e3898006339885c07f9b34e69f8 LaxBloxBoy2 <<EMAIL>> 1751308903 +0200	commit: ✨ CHANGE: Make description text white
e6d873e8c2d59e3898006339885c07f9b34e69f8 0540378f86570acf28ff5731de05d7c42e4bee75 LaxBloxBoy2 <<EMAIL>> 1751331803 +0200	commit: Add MCP Tools sections with hover effects and autocomplete interface
0540378f86570acf28ff5731de05d7c42e4bee75 04363c97510519ab495c636c4f885e0ebc1f8d78 LaxBloxBoy2 <<EMAIL>> 1751424769 +0200	commit: feat: Move UserButton to header and fix Clerk theme styling
04363c97510519ab495c636c4f885e0ebc1f8d78 675f6710b984d51aeeae0868001b3421eb8cbbae LaxBloxBoy2 <<EMAIL>> 1751565225 +0200	commit: feat: Implement comprehensive dashboard and usage tracking system
675f6710b984d51aeeae0868001b3421eb8cbbae a386da75598e753fba88f1335cb9a188b0dc47cb LaxBloxBoy2 <<EMAIL>> 1751565999 +0200	commit: fix: Add fallback URL for documentation link to resolve TypeScript build error
a386da75598e753fba88f1335cb9a188b0dc47cb 14253bb89891e9796a041b9d0e0e47df153b9418 LaxBloxBoy2 <<EMAIL>> 1751566452 +0200	commit: fix: Remove metadata access to resolve TypeScript build error in cost tracking
14253bb89891e9796a041b9d0e0e47df153b9418 f3a0a26b5bb17d240a4c1848e6c5434770515e38 LaxBloxBoy2 <<EMAIL>> 1751566907 +0200	commit: fix: Remove metadata access in requests and tokens pages to resolve TypeScript build errors
f3a0a26b5bb17d240a4c1848e6c5434770515e38 a70de6919421853a32ce360dc0f1ffa65439b502 LaxBloxBoy2 <<EMAIL>> 1751567470 +0200	commit: Fix TypeScript error: Replace extensionSessions access with static value
a70de6919421853a32ce360dc0f1ffa65439b502 b5244308ee11198e3a2041c03e742da6da2b0c2a LaxBloxBoy2 <<EMAIL>> 1751568229 +0200	commit: fix: Resolve duplicate extensionSessions property and restore proper file structure
b5244308ee11198e3a2041c03e742da6da2b0c2a 8e8a3d1b537d80c2fcbad60e87d08314ef86982f LaxBloxBoy2 <<EMAIL>> 1751593884 +0200	commit: Add missing /api/events endpoint for telemetry - fixes token usage tracking
8e8a3d1b537d80c2fcbad60e87d08314ef86982f 6e44eccee3dd16b02c9af69ee464418189cd0acc LaxBloxBoy2 <<EMAIL>> 1751595519 +0200	commit: feat: Add /api/events endpoint for telemetry LLM_COMPLETION events
6e44eccee3dd16b02c9af69ee464418189cd0acc eb6bc7db4a03b2956735b89b3a5f660b1479838b LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Add Clerk JWT authentication to /api/events endpoint
eb6bc7db4a03b2956735b89b3a5f660b1479838b cbe7b458b40e50e3d3f16610486f778b0c2c5699 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Add separate input/output token tracking columns
cbe7b458b40e50e3d3f16610486f778b0c2c5699 53769db10a393abdae3c178d138df7327628b8be LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Remove undefined apiProvider property from events endpoint
53769db10a393abdae3c178d138df7327628b8be 02e7edad26ead46d9415bda8d3d97e4618f6ac95 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Remove undefined properties from events endpoint metadata
02e7edad26ead46d9415bda8d3d97e4618f6ac95 e06ded66d0f646cb3f603fcd9df3961a01a0f884 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: debug: Add better logging for events endpoint authentication
e06ded66d0f646cb3f603fcd9df3961a01a0f884 84cb79fb9ad739ea723cc9133297173125b3f7d9 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Handle Clerk session IDs in events endpoint
84cb79fb9ad739ea723cc9133297173125b3f7d9 20e61c9d70a5766c7385bb270b85d5caaa89f616 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Handle cubent_ext_ tokens in events endpoint
20e61c9d70a5766c7385bb270b85d5caaa89f616 c019bb62bdc812ad64a60d7b594f0f8538866f51 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: debug: Add comprehensive logging to events endpoint database operations
c019bb62bdc812ad64a60d7b594f0f8538866f51 8f48e3c31682bc1833f40c472b4235f917689bd3 LaxBloxBoy2 <<EMAIL>> 1751640314 +0200	commit: fix: Handle both LLM event type formats in events endpoint
8f48e3c31682bc1833f40c472b4235f917689bd3 e35840c926d8c54e0a8ebf11f30200b9643ea853 LaxBloxBoy2 <<EMAIL>> 1751640823 +0200	commit: fix: Convert Clerk user ID to database user ID in events endpoint
e35840c926d8c54e0a8ebf11f30200b9643ea853 75d5efdcca4d890b4cfe93810dbed98a32f584e7 LaxBloxBoy2 <<EMAIL>> 1751649325 +0200	commit: feat: improve website SEO with Cubent logo favicon and domain updates
75d5efdcca4d890b4cfe93810dbed98a32f584e7 ef97016e33194e0e73bc53f67ece047927ca814f LaxBloxBoy2 <<EMAIL>> 1751651358 +0200	commit: feat: update SEO and favicon with new branding
ef97016e33194e0e73bc53f67ece047927ca814f 40a73d096e2e63046554a5573f6ea4c652b34f1c LaxBloxBoy2 <<EMAIL>> 1751807054 +0200	commit: Fix input/output token tracking - use real data instead of estimates
40a73d096e2e63046554a5573f6ea4c652b34f1c 31047735698707ac3de456dac901acc8b518477d LaxBloxBoy2 <<EMAIL>> 1751807357 +0200	commit: Fix TypeScript build errors
31047735698707ac3de456dac901acc8b518477d a69cc6e002ee001f377704759dd7b38dec278dc5 LaxBloxBoy2 <<EMAIL>> 1751807549 +0200	commit: Fix TypeScript type guard for navigation items
a69cc6e002ee001f377704759dd7b38dec278dc5 8c28408134fb214f574095028730b75246b500a4 LaxBloxBoy2 <<EMAIL>> 1751807606 +0200	commit: Fix tokens page database query - include inputTokens and outputTokens fields
8c28408134fb214f574095028730b75246b500a4 c94cedde6cf6ff895b3f98f45851e1bc562bd3f6 LaxBloxBoy2 <<EMAIL>> 1751807804 +0200	commit: Fix PendingLogin database query in units API
c94cedde6cf6ff895b3f98f45851e1bc562bd3f6 5482c6d97d5bc014e3890146b3c3e9891d87a0a4 LaxBloxBoy2 <<EMAIL>> 1751808044 +0200	commit: Fix navigation header TypeScript error with type assertion
5482c6d97d5bc014e3890146b3c3e9891d87a0a4 f71d6295f06cba400ec73482abe93466de31f139 LaxBloxBoy2 <<EMAIL>> 1751851941 +0200	commit: Update login flow UI to match usage/requests background and add orange accents
f71d6295f06cba400ec73482abe93466de31f139 0d18a5458dde08adba012739739f318015ace506 LaxBloxBoy2 <<EMAIL>> 1751852260 +0200	commit: Change welcome text to white in authorization screen
0d18a5458dde08adba012739739f318015ace506 90792701532d184531d21466ec9f9648076387a2 LaxBloxBoy2 <<EMAIL>> 1751853022 +0200	commit: Complete login flow UI overhaul with gradient background and improved UX
90792701532d184531d21466ec9f9648076387a2 4014e57db85fd10fee4f4b498c92d94ca53c0444 LaxBloxBoy2 <<EMAIL>> 1751853712 +0200	commit: Fix login page layout issues - Reverted authenticated layout to original state - Removed duplicate login layout file - Changed login flow from min-h-screen to min-h-full to prevent double header
4014e57db85fd10fee4f4b498c92d94ca53c0444 efa227feaf6af5cd1fe218018977f8fa57e6521a LaxBloxBoy2 <<EMAIL>> 1752356408 +0200	commit: feat: update landing page UI improvements
efa227feaf6af5cd1fe218018977f8fa57e6521a fd6d796d8714873159e85f5749a6fedefa3a1925 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update branding: new logo, gradient icons, footer updates, and pricing text improvements
fd6d796d8714873159e85f5749a6fedefa3a1925 fadb38565eb04a35fd6fcc7eabb58f9b5bc52a21 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update favicons: add rounded black background with white logo for both web and dashboard apps
fadb38565eb04a35fd6fcc7eabb58f9b5bc52a21 7dbcb09f367589ef19bd5be891766bef4a8b84b3 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update dashboard sidebar: hide account/conversations sections, make items bigger, change titles to grey
7dbcb09f367589ef19bd5be891766bef4a8b84b3 feec968e55a5dec935a002ec1053dce831954193 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update sidebar: change Usage Analytics to clickable Cubent Units with lock icon
feec968e55a5dec935a002ec1053dce831954193 24282d253b97b601ccc0b80c0392c081c9ea4ab4 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Move Cubent Units to Usage Metrics section: make it grey, clickable, and replace locked version
24282d253b97b601ccc0b80c0392c081c9ea4ab4 133763d568e03e78423db3f3a85234f98d7cfbe1 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix TypeScript error: remove locked property check from navMain items
133763d568e03e78423db3f3a85234f98d7cfbe1 0c6dc128d2f59f60eba95cd86f4bbb102a96fc89 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Update mobile hamburger menu with #161616 background and expandable Company section
0c6dc128d2f59f60eba95cd86f4bbb102a96fc89 243df65f2c09d4fbdd260922d47df4889a7a47e6 LaxBloxBoy2 <<EMAIL>> 1752456949 +0200	commit: Fix mobile hamburger menu and hero title styling
243df65f2c09d4fbdd260922d47df4889a7a47e6 27148de43ae1514facbc79136050254a2f15a7c8 LaxBloxBoy2 <<EMAIL>> 1752457278 +0200	commit: Update hero title mobile size to 48px and fix line spacing
27148de43ae1514facbc79136050254a2f15a7c8 c2d1f4ae68ecc424c00f43ac26e5af18221f6e5b LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: update authentication pages with clean design
c2d1f4ae68ecc424c00f43ac26e5af18221f6e5b 11fef0de740482a9c3c7c4dae9ade568a393f66e LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: improve app mobile responsiveness and navigation
11fef0de740482a9c3c7c4dae9ade568a393f66e b804d7b338d66071f6e3456d635c69f0a393efd0 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: resolve SidebarProvider context error
b804d7b338d66071f6e3456d635c69f0a393efd0 360d8e8e7a8572412febdc8c4b8bc46b3acae3da LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: improve sidebar layout and mobile responsiveness
360d8e8e7a8572412febdc8c4b8bc46b3acae3da 351452f4c5c9014cc0da9faa127ab782949a4bdd LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: update Documentation link to docs.cubent.dev
351452f4c5c9014cc0da9faa127ab782949a4bdd b43dfa86746a277cdb85ad4b0f747ce2270d5558 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Add logo to sidebar and hide header logo on desktop
b43dfa86746a277cdb85ad4b0f747ce2270d5558 404fa1b04c9e7cfdc367b4f0df8cee5deeef9407 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: biboy
404fa1b04c9e7cfdc367b4f0df8cee5deeef9407 81d558bd621b5205af7d03b3e7be45d0d3ad50b7 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit (amend): biboy
81d558bd621b5205af7d03b3e7be45d0d3ad50b7 3638000fba3bd416a6e85449fe8b4c00cdba999f LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: update profile cards background and disable Cubent Units sidebar navigation
3638000fba3bd416a6e85449fe8b4c00cdba999f 3b3eff8526e930154d844b6c2df247ec8b3198f7 LaxBloxBoy2 <<EMAIL>> 1752537311 +0200	commit: feat: integrate Clerk billing for Start for Free button
3b3eff8526e930154d844b6c2df247ec8b3198f7 325db66df754813c6a996089baa9df1bea8caa45 LaxBloxBoy2 <<EMAIL>> 1752537729 +0200	commit: fix: add missing useUser import to pricing page
325db66df754813c6a996089baa9df1bea8caa45 61e8f7c815f80a760f7cd6a63d705100b50505bf LaxBloxBoy2 <<EMAIL>> 1752538235 +0200	commit: fix: correct sign-in URL to use app.cubent.dev domain
61e8f7c815f80a760f7cd6a63d705100b50505bf 2c3057eef3dd3cf03e42b7f4b72f016767c5f1c2 LaxBloxBoy2 <<EMAIL>> 1752538806 +0200	commit: fix: disable COEP to allow Stripe integration in Clerk billing
2c3057eef3dd3cf03e42b7f4b72f016767c5f1c2 9c758f3120fc306d353e399f0352a276651d3102 LaxBloxBoy2 <<EMAIL>> 1752539522 +0200	commit: feat: add API endpoint for 7-day free trial without credit card
9c758f3120fc306d353e399f0352a276651d3102 740f234001b4736ddaf8b0b72f2bf8874b67dca2 LaxBloxBoy2 <<EMAIL>> 1752539803 +0200	commit: feat: implement 7-day free trial without credit card in pricing page
740f234001b4736ddaf8b0b72f2bf8874b67dca2 6e0fc383a65da04db621e5ea7b2c8eabae026859 LaxBloxBoy2 <<EMAIL>> 1752540043 +0200	commit: fix: correct auth import in start-free-trial API route
6e0fc383a65da04db621e5ea7b2c8eabae026859 f6faaef6fe38c6584b3950735306270c57e48629 LaxBloxBoy2 <<EMAIL>> 1752540220 +0200	commit: fix: correct clerkClient import and usage in start-free-trial API
f6faaef6fe38c6584b3950735306270c57e48629 f146e80aecb20488f34f4c0c8c154cf797d7af1b LaxBloxBoy2 <<EMAIL>> 1752540537 +0200	commit: fix: properly call clerkClient as async function
f146e80aecb20488f34f4c0c8c154cf797d7af1b 1059e1f9ae9fb7ee808ca34313ff323cc88ed5e0 LaxBloxBoy2 <<EMAIL>> 1752541463 +0200	commit: feat: implement complete 7-day free trial system with Clerk webhooks and Stripe
1059e1f9ae9fb7ee808ca34313ff323cc88ed5e0 072c60a1239da5dc8653cc4b029baca87a528846 LaxBloxBoy2 <<EMAIL>> 1752543097 +0200	commit: fix: completely disable Dependabot to stop unwanted deployments
072c60a1239da5dc8653cc4b029baca87a528846 299f6d3ec618ed42be401365c137c02d7105b398 LaxBloxBoy2 <<EMAIL>> 1752543553 +0200	commit: fix: add missing stripe dependency for billing portal
299f6d3ec618ed42be401365c137c02d7105b398 905974357bdc52ac10b7a1af4b969fcde2ec0fb4 LaxBloxBoy2 <<EMAIL>> 1752543769 +0200	commit: fix: resolve TypeScript error in useTrialStatus hook
905974357bdc52ac10b7a1af4b969fcde2ec0fb4 566cc2854052aa1b2a16a04f46eee290b46ddd2f LaxBloxBoy2 <<EMAIL>> 1752777221 +0200	commit: Add TrialBanner to dashboard and fix trial status API
566cc2854052aa1b2a16a04f46eee290b46ddd2f cbc9dc34c36202044d5e25a554fbb5f3af6c2772 LaxBloxBoy2 <<EMAIL>> 1752777449 +0200	commit: Fix TrialBanner cross-workspace import issue
cbc9dc34c36202044d5e25a554fbb5f3af6c2772 0accce078be607711a3ced90dcfbd33ac7b8db35 LaxBloxBoy2 <<EMAIL>> 1752777592 +0200	commit: Fix TrialBanner import path
0accce078be607711a3ced90dcfbd33ac7b8db35 272af0cb3732d406b4946eb6d3e04b5e8101f72e LaxBloxBoy2 <<EMAIL>> 1752778067 +0200	commit: Fix hardcoded URLs in pricing page Start for Free button
272af0cb3732d406b4946eb6d3e04b5e8101f72e 1e99f981184ec8d64cd49bbe104b6ed85409afdd LaxBloxBoy2 <<EMAIL>> 1752779135 +0200	commit: Trigger deployment: Fix Start for Free button and add TrialBanner
1e99f981184ec8d64cd49bbe104b6ed85409afdd 7a41d70b283eddde1c6432f35bc432661359e3ed LaxBloxBoy2 <<EMAIL>> 1752780994 +0200	commit: Debug webhook failures: Add comments and investigate trial creation
7a41d70b283eddde1c6432f35bc432661359e3ed 9c58e0cb6a2cb40814d96a4c00119d13ba96d59f LaxBloxBoy2 <<EMAIL>> 1752782094 +0200	commit: Enhanced webhook debugging: Add accessibility testing and error tracking
9c58e0cb6a2cb40814d96a4c00119d13ba96d59f 3e72b2aca677ed77ce740f03a0d3035bab8466dc LaxBloxBoy2 <<EMAIL>> 1752782971 +0200	commit: Trigger deployment: Enhanced webhook debugging for trial creation
3e72b2aca677ed77ce740f03a0d3035bab8466dc 2edeb0cb48001dcf7ebb8216dd85998916f8311c LaxBloxBoy2 <<EMAIL>> 1752783515 +0200	commit: Major update: Enhanced webhook debugging and trial system fixes
2edeb0cb48001dcf7ebb8216dd85998916f8311c 88552947fc91ccd4733f1b1752a21905e88b6f86 LaxBloxBoy2 <<EMAIL>> 1752784189 +0200	commit: CRITICAL FIX: Resolve 405 Method Not Allowed webhook error
88552947fc91ccd4733f1b1752a21905e88b6f86 940db479aa28dd18a5673d25cc51601a9900d7df LaxBloxBoy2 <<EMAIL>> 1752785198 +0200	commit: EMERGENCY FIX: Simplified webhook route to resolve 404 error
940db479aa28dd18a5673d25cc51601a9900d7df 70bd6ba20f79b8c1ffa6d5e362a71e4c70e99e82 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: CLEAN FIX: Remove duplicate POST functions causing build error
70bd6ba20f79b8c1ffa6d5e362a71e4c70e99e82 2cbfdc07e7ed4664af673b4c368c73ffee41a283 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: TEST: Add alternative webhook route to debug 404 issue
2cbfdc07e7ed4664af673b4c368c73ffee41a283 9e5f6f2c7de9c2576a0289faa6bf9d226bb1dc7f LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: DEBUG: Add simple test API route to verify routing works
9e5f6f2c7de9c2576a0289faa6bf9d226bb1dc7f c5b29145ca3f91e01d71747e776c346e61e9d9e2 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: CRITICAL FIX: Move webhook to app workspace where API routes work
c5b29145ca3f91e01d71747e776c346e61e9d9e2 990a8cebde98c93f180a0918b31acc2dd1a76775 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: COMPLETE TRIAL SYSTEM: Add full webhook functionality
990a8cebde98c93f180a0918b31acc2dd1a76775 be730579d7f900e0386427c4fe349cc771a434a8 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: FIX BUILD: Add missing svix and stripe dependencies to app workspace
be730579d7f900e0386427c4fe349cc771a434a8 b726075e394299ecd6dfc2d748a0c80e833c01b8 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: UX IMPROVEMENT: Move trial banner to compact header badge
b726075e394299ecd6dfc2d748a0c80e833c01b8 a9354a0233d123aab1abd61b27c832300d5a5ee9 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: FIX: Add missing billing portal API route to app workspace
a9354a0233d123aab1abd61b27c832300d5a5ee9 5467f7f12e32f3895d35840092cce4e1d1924e8e LaxBloxBoy2 <<EMAIL>> 1752790277 +0200	commit: FIX: Add payments package to app workspace environment
5467f7f12e32f3895d35840092cce4e1d1924e8e 4ed4b5cb06e729fd2abb3dcd596972ee87f633c2 LaxBloxBoy2 <<EMAIL>> 1752791239 +0200	commit: fix: remove invisible button between trial badge and user profile in dashboard header
4ed4b5cb06e729fd2abb3dcd596972ee87f633c2 fb63a11dec33dacd2ac1eec9a4f91fdad39a1faa LaxBloxBoy2 <<EMAIL>> 1752792373 +0200	commit: feat: implement Byak plan with native Stripe 7-day trial
fb63a11dec33dacd2ac1eec9a4f91fdad39a1faa 39e16a6b8076e73af1ccdd662f46f799eb4d1959 LaxBloxBoy2 <<EMAIL>> 1752793759 +0200	commit: fix: remove hardcoded white text colors from Clerk UserButton dropdown
39e16a6b8076e73af1ccdd662f46f799eb4d1959 b949d31a6b820a3edfde61ce267dea7e9e82a788 LaxBloxBoy2 <<EMAIL>> 1752794123 +0200	commit: style: update CompactTrialBadge to white/grey theme with dark text
b949d31a6b820a3edfde61ce267dea7e9e82a788 2f79a4822b702269f31eab908b63a12aa17eb0c5 LaxBloxBoy2 <<EMAIL>> 1752795405 +0200	commit: style: simplify CompactTrialBadge to pure white background with black text
2f79a4822b702269f31eab908b63a12aa17eb0c5 323f7646ee246362cd70936ce00a2ef252970e44 LaxBloxBoy2 <<EMAIL>> 1752796509 +0200	commit: style: change CompactTrialBadge text back to white
323f7646ee246362cd70936ce00a2ef252970e44 dc9ec73e32c8bd2eb7c1ce5a09ac3940b77b0e4c LaxBloxBoy2 <<EMAIL>> 1752800928 +0200	commit: fix: update extension APIs to read subscription data from Stripe instead of stale database values
dc9ec73e32c8bd2eb7c1ce5a09ac3940b77b0e4c 47371f8d793aecb6ac5d43485a4558b3520c94a2 LaxBloxBoy2 <<EMAIL>> 1752802981 +0200	commit: fix: add missing clerkClient import and user retrieval in auth route
47371f8d793aecb6ac5d43485a4558b3520c94a2 646b93fdea9448ab21435ca64126e9bbc80d9d2e LaxBloxBoy2 <<EMAIL>> 1752803234 +0200	commit: fix: update profile route to use Clerk metadata instead of non-existent stripeCustomerId field
646b93fdea9448ab21435ca64126e9bbc80d9d2e 897c5bb5970b34f3fcec69b26f2f4008fbe5a098 LaxBloxBoy2 <<EMAIL>> 1752848396 +0200	commit: Fix subscription status check - only show paid plans for active subscriptions
897c5bb5970b34f3fcec69b26f2f4008fbe5a098 6e024b6bb1c89c90d7defc857264c7978063ba8e LaxBloxBoy2 <<EMAIL>> 1752849162 +0200	commit: Remove Clerk metadata dependency - use only Stripe data for subscription status
6e024b6bb1c89c90d7defc857264c7978063ba8e ea587de0bf6961d50dc7e62b901d51c7ea4f8ad7 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix subscription detection - query Stripe directly for active subscriptions
ea587de0bf6961d50dc7e62b901d51c7ea4f8ad7 1643c2e54c12c4df268be086476278c71895c337 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Add Manage Subscription button to app header
1643c2e54c12c4df268be086476278c71895c337 ae55ed83b92ae15cd8eb07547bf5ab84f06c6067 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix Manage Subscription button - create Stripe customer on-demand for users without billing account
ae55ed83b92ae15cd8eb07547bf5ab84f06c6067 33b7c835f322ba46f1480eda654eadc36751c5e8 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Add trial days left detection - show 'Plan Name | X Days Left' for trialing subscriptions
33b7c835f322ba46f1480eda654eadc36751c5e8 e68308530266d372a9f25b61e0948fe27784570e LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Add autocomplete tracking and dashboard
e68308530266d372a9f25b61e0948fe27784570e 09c96372260bd55d6a97b9e4ed4209eb86e7155d LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Update autocomplete chart colors to match cost tracking design
09c96372260bd55d6a97b9e4ed4209eb86e7155d aa5047351b45f1542a2d50a8de2dfd448116338f LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Add missing CartesianGrid import in autocomplete chart
aa5047351b45f1542a2d50a8de2dfd448116338f e5d7b98e54a4c2e9015df708d42a0c54f80e2e8a LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Update autocomplete dashboard to match cost tracking design
e5d7b98e54a4c2e9015df708d42a0c54f80e2e8a be8571107611ad118425a12c6bb32c4eba57b4dc LaxBloxBoy2 <<EMAIL>> 1753073029 +0200	commit: feat: Remove autocomplete tracking from web dashboard
be8571107611ad118425a12c6bb32c4eba57b4dc ff82e5afe51e58aaf3e0244302e3bb8de284cfce LaxBloxBoy2 <<EMAIL>> 1753200204 +0200	commit: feat: implement comprehensive autocomplete tracking system
ff82e5afe51e58aaf3e0244302e3bb8de284cfce 118dd859253faf73b6e1f293315eebc1dc7a2d1d LaxBloxBoy2 <<EMAIL>> 1753200479 +0200	commit: fix: correct UI component imports for autocomplete dashboard
118dd859253faf73b6e1f293315eebc1dc7a2d1d c022a555fa6e2e861c1686b1c772297b226c1b6f LaxBloxBoy2 <<EMAIL>> 1753201561 +0200	commit: fix: redesign autocomplete dashboard and fix tracking implementation
c022a555fa6e2e861c1686b1c772297b226c1b6f 20c04b2e53b5edb30e1060d3bba3cba935c6ff8c LaxBloxBoy2 <<EMAIL>> 1753207412 +0200	commit: debug: add comprehensive logging to autocomplete tracking
20c04b2e53b5edb30e1060d3bba3cba935c6ff8c 48498b8147f6e1cef81d8d2cd951a82b8fd75fba LaxBloxBoy2 <<EMAIL>> 1753207913 +0200	commit: fix: add proper extension authentication to autocomplete tracking API
48498b8147f6e1cef81d8d2cd951a82b8fd75fba b261184463083042231bc8121ce045675469a2ab LaxBloxBoy2 <<EMAIL>> 1753209085 +0200	commit: ui: improve autocomplete dashboard design
b261184463083042231bc8121ce045675469a2ab 1b4ec955aec5beb72d0054f1e394aa05c5c20af3 LaxBloxBoy2 <<EMAIL>> 1753209487 +0200	commit: ui: improve recent usage column layout
1b4ec955aec5beb72d0054f1e394aa05c5c20af3 e7c94cbd93cf9ed95c92767bd33950f8c07c27e8 LaxBloxBoy2 <<EMAIL>> 1753209857 +0200	commit: ui: improve recent usage table layout and add pagination
e7c94cbd93cf9ed95c92767bd33950f8c07c27e8 5901d5c5e30ff27a2093a3675e6ca44316f92976 LaxBloxBoy2 <<EMAIL>> 1753210394 +0200	commit: fix: implement lines per page functionality and reduce date text size
5901d5c5e30ff27a2093a3675e6ca44316f92976 0f171c13977c08b6222ec69e851144333d4698f2 LaxBloxBoy2 <<EMAIL>> 1753211182 +0200	commit: feat: add column headers and pagination to recent usage table
0f171c13977c08b6222ec69e851144333d4698f2 c520d9c5516f38b12a593f1afc185fcf94a97904 LaxBloxBoy2 <<EMAIL>> 1753211623 +0200	commit: fix: remove model breakdown and fix data structure issues
c520d9c5516f38b12a593f1afc185fcf94a97904 2e1e115e3c781bd3b1cafa4e085142b6a604aece LaxBloxBoy2 <<EMAIL>> 1753211984 +0200	commit: remove model breakdown section completely
2e1e115e3c781bd3b1cafa4e085142b6a604aece 3ab6b75f69208454cea583e8e1192c9e71d07f0b LaxBloxBoy2 <<EMAIL>> 1753213345 +0200	commit: fix: clean up API response structure for autocomplete data
3ab6b75f69208454cea583e8e1192c9e71d07f0b 3dec9c1bb7d3bfb51574afdd6dd21aaa0c078a19 LaxBloxBoy2 <<EMAIL>> 1753214285 +0200	commit: fix: correct lines calculation in autocomplete tracking
3dec9c1bb7d3bfb51574afdd6dd21aaa0c078a19 d8a899c5240590660b388a099d1b5f55d0ccbc6e LaxBloxBoy2 <<EMAIL>> 1753219169 +0200	commit: fix: update homepage hero buttons - remove JetBrains, add lock icon to VS Code
d8a899c5240590660b388a099d1b5f55d0ccbc6e b92660aa4e5fd6ba74bd358e74d839d4a49ec81d LaxBloxBoy2 <<EMAIL>> 1753233646 +0200	commit: Update web app metadata to use 'Cubent Console' branding
b92660aa4e5fd6ba74bd358e74d839d4a49ec81d e1d613497d21f280b144860af853c0ed475c8975 LaxBloxBoy2 <<EMAIL>> 1753233899 +0200	commit: Add missing createAppMetadata function for web app branding
e1d613497d21f280b144860af853c0ed475c8975 fa5cffab6aa089f3a55becef3c0d5e50d3699d2d LaxBloxBoy2 <<EMAIL>> 1753234952 +0200	commit: Update dashboard, profile pages, and SEO metadata
fa5cffab6aa089f3a55becef3c0d5e50d3699d2d afd4cf70a92c401318c6d2b6b590b0d3235e1157 LaxBloxBoy2 <<EMAIL>> 1753235213 +0200	commit: Update trial and subscription system with improved error handling and UI components
afd4cf70a92c401318c6d2b6b590b0d3235e1157 5a0db84804f3dfa0c0bca9257128e525e9277972 LaxBloxBoy2 <<EMAIL>> 1753405321 +0200	commit: Fix webapp logout and Stripe trial settings
5a0db84804f3dfa0c0bca9257128e525e9277972 c9edfdc6b8709e4fba2a07bc248940433f4506bb LaxBloxBoy2 <<EMAIL>> 1753411409 +0200	commit: Remove gradient backgrounds and set solid #1a1a1a background to match usage section
c9edfdc6b8709e4fba2a07bc248940433f4506bb 8bd1b3718af9d937f2a5308abf86476aee5be9fb LaxBloxBoy2 <<EMAIL>> 1753412880 +0200	commit: Fix login screen styling: gradient email, transparent checkbox, borderless buttons
8bd1b3718af9d937f2a5308abf86476aee5be9fb ceda3102dd4080b54d2bd5713997a50356399e1b LaxBloxBoy2 <<EMAIL>> 1753413254 +0200	commit: Fix login page: remove header/sidebar, fix checkbox background
ceda3102dd4080b54d2bd5713997a50356399e1b cb8451548ac9cf421421bbb0e918053cbdd930cd LaxBloxBoy2 <<EMAIL>> 1753413709 +0200	commit: Complete login page redesign: grid background, white card, external title
cb8451548ac9cf421421bbb0e918053cbdd930cd 17f148d0c3e729fafceddf6d2f03550fcf36f94b LaxBloxBoy2 <<EMAIL>> 1753413899 +0200	commit: Fix syntax error: remove duplicate closing parenthesis
17f148d0c3e729fafceddf6d2f03550fcf36f94b 33d5a01424f24d863b430994922a53ed4f5adf25 LaxBloxBoy2 <<EMAIL>> 1753414210 +0200	commit: Fix JSX syntax: remove extra closing div tag
33d5a01424f24d863b430994922a53ed4f5adf25 5a453d26abd7ef6b7ab04bb537516bceaad86f63 LaxBloxBoy2 <<EMAIL>> 1753414740 +0200	commit: Move login page to unauthenticated route group with grid background and white card
5a453d26abd7ef6b7ab04bb537516bceaad86f63 db1af6793db3e14ac185277aa037b4779e689fc7 LaxBloxBoy2 <<EMAIL>> 1753415476 +0200	commit: Make login card more compact with dark button background
db1af6793db3e14ac185277aa037b4779e689fc7 5a39aef85cb6983c4fac305a0a17a2fc06aaa038 LaxBloxBoy2 <<EMAIL>> 1753416479 +0200	commit: Update success icon to dark background with white checkmark, remove email gradient, make title white
5a39aef85cb6983c4fac305a0a17a2fc06aaa038 29437fc7a3d74792833036ec8ba60eb6a55f3f6c LaxBloxBoy2 <<EMAIL>> 1753417150 +0200	commit: Add collapsible terms section with show more/less toggle
29437fc7a3d74792833036ec8ba60eb6a55f3f6c 89c21c6dbfa47f0eb0b4c7c545f95575852f9254 LaxBloxBoy2 <<EMAIL>> 1753417493 +0200	commit: Update checkbox to circular with white background and dark checkmark
89c21c6dbfa47f0eb0b4c7c545f95575852f9254 da9286202efaa9233c1409a2ca5cc0afe6339fd5 LaxBloxBoy2 <<EMAIL>> 1753418345 +0200	commit: Update success icon: white circle background with dark checkmark and light border
da9286202efaa9233c1409a2ca5cc0afe6339fd5 535d3f5d0c95a6532fbb815882914c05c4eac4b6 LaxBloxBoy2 <<EMAIL>> 1753450252 +0200	commit: Complete SEO audit fixes: Add missing robots.txt, sitemap.xml, manifest.json, favicon files, 404 page, structured data, performance optimizations, and social sharing components
535d3f5d0c95a6532fbb815882914c05c4eac4b6 9df5013bdd1e3be2b026a3433b30829bbd4cc716 LaxBloxBoy2 <<EMAIL>> 1753450466 +0200	commit: Fix TypeScript error: Handle null textContent in performance optimizer
9df5013bdd1e3be2b026a3433b30829bbd4cc716 94093d2cf53824c5f58efa814d72e08fb85a6210 LaxBloxBoy2 <<EMAIL>> 1753450726 +0200	commit: Fix social share component: Remove sonner dependency and deprecated icons
94093d2cf53824c5f58efa814d72e08fb85a6210 01bcbeeddf1ab64e0bb62531842bc53548d8fe31 LaxBloxBoy2 <<EMAIL>> 1753451195 +0200	commit: Fix critical DOM manipulation error: Simplify performance optimizer to avoid React conflicts
01bcbeeddf1ab64e0bb62531842bc53548d8fe31 174309148360518fb1b9f73a14451d96190480d8 LaxBloxBoy2 <<EMAIL>> 1753453068 +0200	commit: Fix SEO issues: Add proper 404 page, error boundary, console error suppression, image optimization, and canonical URLs
174309148360518fb1b9f73a14451d96190480d8 70c4349e1a3ca2adc589b0f17c0af08dbd20af5d LaxBloxBoy2 <<EMAIL>> 1753453514 +0200	commit: Implement automatic AVIF image conversion: 60-70% size reduction, build integration, aggressive compression for maximum SEO performance
70c4349e1a3ca2adc589b0f17c0af08dbd20af5d 97faf3dd12fc5777a0018b4a0d609114c4a4ec45 LaxBloxBoy2 <<EMAIL>> 1753453680 +0200	commit: Fix build errors: Remove conflicting root not-found.tsx, make AVIF conversion optional for Vercel builds
97faf3dd12fc5777a0018b4a0d609114c4a4ec45 6053c8f6b596b54a5d46f5cc7abf89bb486304da LaxBloxBoy2 <<EMAIL>> 1753453858 +0200	commit: Remove problematic webpack config causing file-loader errors in Vercel builds
6053c8f6b596b54a5d46f5cc7abf89bb486304da df79f7ad4f3d394ea4c811a8fc48e86799afa3dd LaxBloxBoy2 <<EMAIL>> 1753454671 +0200	commit: Fix robots.txt and 404 page: Remove conflicting dynamic robots.ts, add proper global not-found.tsx
df79f7ad4f3d394ea4c811a8fc48e86799afa3dd bdbd67ae709c16e80a308fca67897d078c5ca466 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix not-found.tsx layout error: Remove html/body tags to use root layout properly
bdbd67ae709c16e80a308fca67897d078c5ca466 8f2a419ae49e2a41cead31db12d63d3f6d8afad3 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Remove global not-found.tsx: Use locale-specific 404 page for internationalized app
8f2a419ae49e2a41cead31db12d63d3f6d8afad3 50e875fef49380093feaef1ad97c208a4ad2f644 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Add root layout and global not-found page: Fix 404 page routing for internationalized app
50e875fef49380093feaef1ad97c208a4ad2f644 6bbaa49686502703c4c13431433311520c849589 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Fix root 404 styling: Add proper CSS imports and design system to root layout
6bbaa49686502703c4c13431433311520c849589 a9301e3c5e7bdf7b3c45834d0a9e92549366e0d4 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Complete SEO fixes: robots.txt optimized, favicon consistency, header on root 404, #161616 background with grainy filter on all 404 pages
a9301e3c5e7bdf7b3c45834d0a9e92549366e0d4 3bbf8a69ea8accb4f7f8cdbe4a0cdd02e012d69f LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Simple fixes: 404 background #161616, root favicon, sitemap priorities - no other changes
3bbf8a69ea8accb4f7f8cdbe4a0cdd02e012d69f 6eb0eb928a4ecc2aa0c992354f1ab6f13a15d8fb LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: Make 404 buttons white and links white on dark background
6eb0eb928a4ecc2aa0c992354f1ab6f13a15d8fb 32b91425717ec2a4d1d06bc44f6ebd73cf043339 LaxBloxBoy2 <<EMAIL>> 1753459027 +0200	commit: Convert GIFs to WebP: 38-41% file size reduction, improved performance
32b91425717ec2a4d1d06bc44f6ebd73cf043339 f24f46602d28cfe14d209476c71b1c54fbbb8ff2 LaxBloxBoy2 <<EMAIL>> 1753460961 +0200	commit: Remove '| Cubent' suffix from homepage SEO title
f24f46602d28cfe14d209476c71b1c54fbbb8ff2 77407da354cdeed8f022870741599b7cabc5e2a0 LaxBloxBoy2 <<EMAIL>> 1753461593 +0200	commit: Update homepage SEO: Next-Gen Agentic AI title and BYAK description
77407da354cdeed8f022870741599b7cabc5e2a0 38ef3c5cf7525bfc44df072e47d1502be761860e LaxBloxBoy2 <<EMAIL>> 1753462676 +0200	commit: Update SEO titles, compact blog layout: Pricing & Blog metadata + 3-column compact blog grid
38ef3c5cf7525bfc44df072e47d1502be761860e 6b33f0766d08295d619ebeba6d35aca2251a8cf2 LaxBloxBoy2 <<EMAIL>> 1753463249 +0200	commit: Fix pricing page build error: Move 'use client' to top, create separate layout for metadata
6b33f0766d08295d619ebeba6d35aca2251a8cf2 934ea9d225151c9fffd0df88d67dce0712d80b9b LaxBloxBoy2 <<EMAIL>> 1753497931 +0200	commit: Fix dashboard graph to show day-by-day input/output tokens and update controls
934ea9d225151c9fffd0df88d67dce0712d80b9b 91429f56f9a1ee2db639c1b9eb8b9b44f5a52c5f LaxBloxBoy2 <<EMAIL>> 1753498518 +0200	commit: Fix dashboard color and navigation issues
91429f56f9a1ee2db639c1b9eb8b9b44f5a52c5f 68a3c152984a665638da6d3a009d62ce661c57e5 LaxBloxBoy2 <<EMAIL>> 1753500052 +0200	commit: Fix dashboard UI and SEO metadata issues
68a3c152984a665638da6d3a009d62ce661c57e5 9e66dc87f5be1192f8b249299ceae40b5851fbad LaxBloxBoy2 <<EMAIL>> 1753500514 +0200	commit: Fix blog page layout and styling
9e66dc87f5be1192f8b249299ceae40b5851fbad ea28c86b4b504f1482b6b2251bb3c2e93977e1cc LaxBloxBoy2 <<EMAIL>> 1753500955 +0200	commit: Fix blog page layout and header styling
ea28c86b4b504f1482b6b2251bb3c2e93977e1cc 3c1aeac9e130814a4ef0b29f85db45bd7274a601 LaxBloxBoy2 <<EMAIL>> 1753501335 +0200	commit: Improve blog page layout and fix header background
3c1aeac9e130814a4ef0b29f85db45bd7274a601 821cb130fb729068427ba2917ca1053bf38505ff LaxBloxBoy2 <<EMAIL>> 1753501462 +0200	commit: Fix blog layout build error
821cb130fb729068427ba2917ca1053bf38505ff 8a70e55cff0a98d58d4d246a050553e2ca1488c2 LaxBloxBoy2 <<EMAIL>> 1753501804 +0200	commit: Reduce blog page padding and extend background to cover header area
8a70e55cff0a98d58d4d246a050553e2ca1488c2 63dd4be34ee753a729ffccba928ff5c65ab91d1c LaxBloxBoy2 <<EMAIL>> 1753502277 +0200	commit: Add small padding above Blog title
63dd4be34ee753a729ffccba928ff5c65ab91d1c f8aab5f4ade6026d6fc07b16d0310026ed9b0778 LaxBloxBoy2 <<EMAIL>> 1753503985 +0200	commit: Apply footer background to blog post pages
f8aab5f4ade6026d6fc07b16d0310026ed9b0778 e1d9792e489d5c927478893deb0bc370f3ec8521 LaxBloxBoy2 <<EMAIL>> 1753540147 +0200	commit: Add sitemap.xml route
e1d9792e489d5c927478893deb0bc370f3ec8521 edbcb4abd2d585f36f057e36e593d23ae38673ed LaxBloxBoy2 <<EMAIL>> 1753540425 +0200	commit: Fix sitemap error by removing conflicting sitemap.ts file
edbcb4abd2d585f36f057e36e593d23ae38673ed 91efd2d0a755fe240be9413493a0803d1b445d57 LaxBloxBoy2 <<EMAIL>> 1753546908 +0200	commit: Redesign Company dropdown menu
91efd2d0a755fe240be9413493a0803d1b445d57 e59d99b00a68090e219969e4f387de2a8244f73d LaxBloxBoy2 <<EMAIL>> 1753547189 +0200	commit: Match dropdown background to footer color with glassy effect
e59d99b00a68090e219969e4f387de2a8244f73d 881fb965d486ae7660f6a1f05d38e5874fae7c04 LaxBloxBoy2 <<EMAIL>> 1753558881 +0200	commit: Fix dashboard chart x-axis labels not updating when month changes
881fb965d486ae7660f6a1f05d38e5874fae7c04 89095ab873830e52168ce558069bb2d282787057 LaxBloxBoy2 <<EMAIL>> 1753563854 +0200	commit: Fix usage dashboard chart date range mismatch
89095ab873830e52168ce558069bb2d282787057 dd3dc8b8993595d13cafe7a7b209ff8707547f24 LaxBloxBoy2 <<EMAIL>> 1753587927 +0200	commit: Fill missing days in monthly chart view
dd3dc8b8993595d13cafe7a7b209ff8707547f24 a8e6c64964a2bb94af2080c9ddd77eed2bb08b31 LaxBloxBoy2 <<EMAIL>> 1753588279 +0200	commit: Replace rate-limited requests with recent requests section
a8e6c64964a2bb94af2080c9ddd77eed2bb08b31 099bbbba701e7ff0c3bdf3752a0c7a57e38a03df LaxBloxBoy2 <<EMAIL>> 1753588599 +0200	commit: Make recent requests more compact and add proper navigation
099bbbba701e7ff0c3bdf3752a0c7a57e38a03df 8c7c07f11ef9506b9535cf335905c2b2b5e3259d LaxBloxBoy2 <<EMAIL>> 1753588882 +0200	commit: Optimize recent requests layout for better space usage
8c7c07f11ef9506b9535cf335905c2b2b5e3259d 219e62d8656ceef0586679306edba34d2793606b LaxBloxBoy2 <<EMAIL>> 1753589488 +0200	commit: Complete UI cleanup tasks: Remove dropdowns and confirm chart exists
219e62d8656ceef0586679306edba34d2793606b a7278fc66d617796b64e5fe2ca471fdd766e01f1 LaxBloxBoy2 <<EMAIL>> 1753590039 +0200	commit: Update favicon and fix request chart to show all 30 days
a7278fc66d617796b64e5fe2ca471fdd766e01f1 27f67be875ed549aa176da33ca9728b0fbb8f909 LaxBloxBoy2 <<EMAIL>> 1753590495 +0200	commit: Fix app favicon to match website design
27f67be875ed549aa176da33ca9728b0fbb8f909 b4f43069b17a4234fe844cc8ad60a798736531c8 LaxBloxBoy2 <<EMAIL>> 1753591022 +0200	commit: Copy exact website favicon to app
b4f43069b17a4234fe844cc8ad60a798736531c8 f8e4f59be51eb620c59be725c9814dbca9055ea6 LaxBloxBoy2 <<EMAIL>> 1753619459 +0200	commit: Fix app favicon to match website exactly
f8e4f59be51eb620c59be725c9814dbca9055ea6 87fff5da6e5057bf724e1d92bbad318dcc4eaa73 LaxBloxBoy2 <<EMAIL>> 1753619700 +0200	commit: Remove static favicon files that were overriding dynamic icon
87fff5da6e5057bf724e1d92bbad318dcc4eaa73 281a32d4d0b982fc447701980c3e9f225af08518 LaxBloxBoy2 <<EMAIL>> 1753620791 +0200	commit: Update favicon across website and app to use correct Cubent.Dev favicon
281a32d4d0b982fc447701980c3e9f225af08518 bf2fa781090702bdb794b02a860134649af3fcc4 LaxBloxBoy2 <<EMAIL>> 1753621220 +0200	commit: Replace all favicon.ico files with correct Cubent.Dev-_41_.ico and remove PNG favicons
bf2fa781090702bdb794b02a860134649af3fcc4 3dc595e9a0189e3cecbeadcf6b34733d4f4130b8 LaxBloxBoy2 <<EMAIL>> 1753621558 +0200	commit: Remove all dynamic icon.tsx files to use only static favicon.ico
3dc595e9a0189e3cecbeadcf6b34733d4f4130b8 aede29d9696abffd2a09a8c0e863b69e3ae2ccbc LaxBloxBoy2 <<EMAIL>> 1753621980 +0200	commit: Remove ALL remaining icon files and clean build cache
aede29d9696abffd2a09a8c0e863b69e3ae2ccbc b5c8b6c8c6af53d6506c7333e1762b495f4f3068 LaxBloxBoy2 <<EMAIL>> 1753623952 +0200	commit: Fix token consumption graph to show all 30 days
b5c8b6c8c6af53d6506c7333e1762b495f4f3068 2b153b0b142ea18ebe2fc9eebd0fe8adff7f6c59 LaxBloxBoy2 <<EMAIL>> 1753624514 +0200	commit: Fix cost consumption graph to show all 30 days
2b153b0b142ea18ebe2fc9eebd0fe8adff7f6c59 2172786ea258237dab5a813fce8940c8d1b569aa LaxBloxBoy2 <<EMAIL>> 1753625008 +0200	commit: Fix dashboard graph date display issue
2172786ea258237dab5a813fce8940c8d1b569aa d66eb2ca6f0fc455431e8f2cdf4eac39ee6e7ab0 LaxBloxBoy2 <<EMAIL>> 1753625669 +0200	commit: Remove 'Last 30 days' dropdown and 'Export' buttons from usage pages
d66eb2ca6f0fc455431e8f2cdf4eac39ee6e7ab0 a122e9453cfd6be1b35c5d2c5fdcf041cf049f1d LaxBloxBoy2 <<EMAIL>> 1753626230 +0200	commit: Add 'Cubent Workspace' SEO title to all authenticated app pages
a122e9453cfd6be1b35c5d2c5fdcf041cf049f1d a69791f1a504521bc8057d130f892a3c292f67de LaxBloxBoy2 <<EMAIL>> 1753626295 +0200	commit: Add 'Cubent Workspace' title to login page
a69791f1a504521bc8057d130f892a3c292f67de 6638da987d9f72e4906c68323c8527b2eeb0ca27 LaxBloxBoy2 <<EMAIL>> 1753626609 +0200	commit: Fix build error: Replace removed title/description variables with hardcoded strings
6638da987d9f72e4906c68323c8527b2eeb0ca27 86b6f83164f244933285d080b4076cb0aaadd9f2 LaxBloxBoy2 <<EMAIL>> 1753626711 +0200	commit: Fix terms page: Replace undefined title/description variables
86b6f83164f244933285d080b4076cb0aaadd9f2 c08483da933e4b044cd8c90b2bb679aef6f73ad7 LaxBloxBoy2 <<EMAIL>> 1753640796 +0200	commit: Remove VS Code Extension card from profile page
c08483da933e4b044cd8c90b2bb679aef6f73ad7 d26eb6e7380c3d5b2b585be601d0ebbb33b095a5 LaxBloxBoy2 <<EMAIL>> 1753641198 +0200	commit: Fix subscription plan display to show actual user plan
d26eb6e7380c3d5b2b585be601d0ebbb33b095a5 67a50fb5ba550ae28187f1cef5fb5885d01b08ca LaxBloxBoy2 <<EMAIL>> 1753740757 +0200	commit: feat: Add animated GIF demo to homepage
67a50fb5ba550ae28187f1cef5fb5885d01b08ca b6397ee24a3da21059552ebbb516b61f8e44a9cb LaxBloxBoy2 <<EMAIL>> 1753742255 +0200	commit: feat: Enhance GIF demo section layout
b6397ee24a3da21059552ebbb516b61f8e44a9cb 6c29b2db29f4a072b65d9ee09869d3cc80008c0b LaxBloxBoy2 <<EMAIL>> 1753743334 +0200	commit: feat: Improve GIF demo positioning
6c29b2db29f4a072b65d9ee09869d3cc80008c0b ea6d01caef59949f0015b2199b72154397808552 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Update GIF positioning and replace mockup images
ea6d01caef59949f0015b2199b72154397808552 2dbe44ca80326a188bf31950f3004748706e8c89 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Update interface images and improve GIF header transparency
2dbe44ca80326a188bf31950f3004748706e8c89 2226406be3ea6148f376f7f583a80e76e61d3ec3 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Reorganize homepage sections - move Speed section after Model Providers
2226406be3ea6148f376f7f583a80e76e61d3ec3 fcbf8d576d98d79cb1c94cb726192c1624503af2 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Update VS Code button to link to marketplace
fcbf8d576d98d79cb1c94cb726192c1624503af2 729d3cae1b5611a9a76c8e88154670809e66dd67 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: fix: Make VS Code button properly white and remove bold text
729d3cae1b5611a9a76c8e88154670809e66dd67 35e847f8f90a0e699f9d4da9199f6457d523c8bb LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Update VS Code button colors and font weight
35e847f8f90a0e699f9d4da9199f6457d523c8bb 2a6edc585afc2255315f950ee93e09b878106b05 LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Redesign VS Code button with dark theme styling
2a6edc585afc2255315f950ee93e09b878106b05 55afa5c9b031d269e01560059e65e783bd0e79ef LaxBloxBoy2 <<EMAIL>> ********** +0200	commit: feat: Reverse VS Code button colors for better contrast
55afa5c9b031d269e01560059e65e783bd0e79ef 766cbc433200ac79a076ad0433e3763a87284428 LaxBloxBoy2 <<EMAIL>> 1753758861 +0200	commit: feat: Add hover color inversion and adjust VS Code icon spacing
766cbc433200ac79a076ad0433e3763a87284428 3e4f008663388c4faf693518925ea6ac2e3cddde LaxBloxBoy2 <<EMAIL>> 1753759209 +0200	commit: fix: Replace JavaScript event handlers with CSS-only hover effects
3e4f008663388c4faf693518925ea6ac2e3cddde 676bd9025576d5b4e56740dacb1f5857708f03e2 LaxBloxBoy2 <<EMAIL>> 1753759715 +0200	commit: feat: Reduce VS Code button width for better proportions
676bd9025576d5b4e56740dacb1f5857708f03e2 19bf81e011c9459a55b511f2c8a348ffc10f53a9 LaxBloxBoy2 <<EMAIL>> 1753760057 +0200	commit: feat: Remove colorful decorative lines from hero section
19bf81e011c9459a55b511f2c8a348ffc10f53a9 1ff248ca0d185b618de0c2401c6db96339b5e3ef LaxBloxBoy2 <<EMAIL>> 1753760817 +0200	commit: Make hero section radial gradient more visible and improve GIF container header transparency
1ff248ca0d185b618de0c2401c6db96339b5e3ef 23a90916d11bbef2c6ea4c4258ac923646c24d61 LaxBloxBoy2 <<EMAIL>> 1753761495 +0200	commit: Update release version to 0.31.4 and make Install in VS Code button shorter
23a90916d11bbef2c6ea4c4258ac923646c24d61 ceca9cdc63d7a1a1878e4d8bb407439b741c24b6 LaxBloxBoy2 <<EMAIL>> 1753761817 +0200	commit: Make Install in VS Code button shorter in width while maintaining height
ceca9cdc63d7a1a1878e4d8bb407439b741c24b6 baca169de08706d2eeba96ef1eca4f84a3dc2c5d LaxBloxBoy2 <<EMAIL>> 1753926722 +0200	commit: Update documentation: favicon, logo styling, and Download Extension button
baca169de08706d2eeba96ef1eca4f84a3dc2c5d 0bb08bc6beea48184414b6fd02cc8faa50449fef LaxBloxBoy2 <<EMAIL>> 1753927016 +0200	commit: Fix hardcoded white accents - make colors theme-aware
0bb08bc6beea48184414b6fd02cc8faa50449fef 30d9f234a57a6cdaf619cf00469fc30440cc71f8 LaxBloxBoy2 <<EMAIL>> 1754172064 +0200	commit: Update homepage 'Read the docs' button to point to docs.cubent.dev
30d9f234a57a6cdaf619cf00469fc30440cc71f8 c79688c59c0883d009244bb9087af17a02b7139b LaxBloxBoy2 <<EMAIL>> 1754359043 +0200	commit: Reduce hero text size on mobile from 56px to 42px for better mobile UX
c79688c59c0883d009244bb9087af17a02b7139b c6c357e5349fb3eabdf549a2fbb696d234f96b13 LaxBloxBoy2 <<EMAIL>> 1754504666 +0200	commit: Update images for Autocomplete and MCP Tools sections
c6c357e5349fb3eabdf549a2fbb696d234f96b13 5a168603806634df2f58747866f88d20c11c9534 LaxBloxBoy2 <<EMAIL>> 1754532408 +0200	commit: Update all section images with new versions
5a168603806634df2f58747866f88d20c11c9534 cef32c43dc91080358e2dc4dceecbd149902e5ee LaxBloxBoy2 <<EMAIL>> 1754533917 +0200	commit: Fix auth flow and UI issues
cef32c43dc91080358e2dc4dceecbd149902e5ee d728e0e248d64b15af674356e420b0ce67e30793 LaxBloxBoy2 <<EMAIL>> 1754534645 +0200	commit: Fix TypeScript error in sign-up page
